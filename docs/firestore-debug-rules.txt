rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }

    function isAdmin() {
      return isSignedIn() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.get('isAdmin', false) == true;
    }

    // USERS - Simplified for debugging
    match /users/{userId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn() && request.auth.uid == userId;
      allow update: if isSignedIn() && (isAdmin() || request.auth.uid == userId);
      allow delete: if isAdmin() && request.auth.uid != userId;
    }

    // WISHLISTS - Simplified for debugging
    match /wishlists/{userId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn() && request.auth.uid == userId;
      allow update: if isSignedIn() && (request.auth.uid == userId || isAdmin());
      allow delete: if isAdmin();
    }

    // ITEMS - Simplified for debugging
    match /items/{itemId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if isSignedIn();
      allow delete: if isSignedIn();
    }

    // MESSAGES
    match /messages/{messageId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if isSignedIn();
      allow delete: if isSignedIn();
    }

    // EVENTS
    match /events/{eventId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // CATEGORIES
    match /categories/{categoryId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();
    }

    // GROUPS
    match /groups/{groupId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();
    }

    // RANKS
    match /ranks/{rankId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();
    }
  }
}