rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }

    function isMe(userId) {
      return request.auth.uid == userId;
    }

    function isAdmin() {
      return isSignedIn() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.get('isAdmin', false) == true;
    }

    // Function to check if users share a group
    function sharesGroupWith(userA, userB) {
      return exists(/databases/$(database)/documents/users/$(userA)) &&
             exists(/databases/$(database)/documents/users/$(userB)) &&
             get(/databases/$(database)/documents/users/$(userA)).data.get('groups', []).hasAny(
               get(/databases/$(database)/documents/users/$(userB)).data.get('groups', [])
             );
    }

    // USERS
    match /users/{userId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn() && request.auth.uid == userId;
      allow update: if isSignedIn() && (
        isAdmin() || 
        (request.auth.uid == userId && 
         // Protect admin-only fields
         (!request.writeFields.has('isAdmin') || request.resource.data.isAdmin == resource.data.isAdmin) &&
         (!request.writeFields.has('isApproved') || request.resource.data.isApproved == resource.data.isApproved) &&
         (!request.writeFields.has('groups') || request.resource.data.groups == resource.data.groups))
      );
      allow delete: if isAdmin() && request.auth.uid != userId;
    }

    // WISHLISTS (shells)
    match /wishlists/{userId} {
      allow read: if isSignedIn() && (isMe(userId) || sharesGroupWith(request.auth.uid, userId) || isAdmin());
      allow create: if isSignedIn() && (isMe(userId) || isAdmin());
      allow update: if isSignedIn() && (isMe(userId) || isAdmin());
      allow delete: if isAdmin();
    }


    // ITEMS (wishlist items)
    match /items/{itemId} {
      allow read: if isSignedIn() && (resource.data.ownerUid == request.auth.uid || sharesGroupWith(request.auth.uid, resource.data.ownerUid) || isAdmin());
      allow create: if isSignedIn() && request.resource.data.ownerUid == request.auth.uid;
      allow update: if isSignedIn() && (
        isAdmin() || 
        resource.data.ownerUid == request.auth.uid ||
        (sharesGroupWith(request.auth.uid, resource.data.ownerUid) && request.writeFields.hasOnly(['commitments', 'updatedAt']))
      );
      allow delete: if isAdmin() || (isSignedIn() && resource.data.ownerUid == request.auth.uid);
    }

    // MESSAGES (notifications)
    match /messages/{messageId} {
      allow read: if isSignedIn() && (resource.data.recipientUid == request.auth.uid || isAdmin());
      allow create: if isSignedIn();
      allow update: if isSignedIn() && (resource.data.recipientUid == request.auth.uid || isAdmin());
      allow delete: if isAdmin() || (isSignedIn() && resource.data.recipientUid == request.auth.uid);
    }

    // EVENTS
    match /events/{eventId} {
      allow read: if isSignedIn();
      allow create: if isSignedIn() && (isAdmin() || request.resource.data.createdByUserId == request.auth.uid);
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // CATEGORIES
    match /categories/{categoryId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();
    }

    // GROUPS
    match /groups/{groupId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();
    }

    // RANKS
    match /ranks/{rankId} {
      allow read: if isSignedIn();
      allow create, update, delete: if isAdmin();
    }


  }
}
