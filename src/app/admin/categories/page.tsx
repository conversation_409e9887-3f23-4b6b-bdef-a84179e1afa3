
"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  useWishlist,
  getTotalCommittedQuantity // Import directly
} from '@/contexts/WishlistContext';
import type { Category, WishlistItem as WishlistItemType, User } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from '@/components/ui/badge';
import { PlusCircle, Edit, Trash2, ShieldAlert, ShoppingBasket, Loader2 } from 'lucide-react'; // Added Loader2
import Link from 'next/link';
import Image from 'next/image';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription as DialogDescriptionComponent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription as AlertDialogDescriptionDialog,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle as AlertDialogTitleComponent,
  AlertDialogTrigger as AlertDialogTriggerComponent,
} from "@/components/ui/alert-dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from '@/hooks/use-toast';
import { AddWishlistItemForm } from '@/components/wishlists/AddWishlistItemForm';
import { Textarea } from '@/components/ui/textarea';

const categoryFormSchema = z.object({
  title: z.string().min(2, "Category title must be at least 2 characters.").max(50),
});
type CategoryFormValues = z.infer<typeof categoryFormSchema>;

interface EnrichedWishlistItem extends WishlistItemType {
  ownerName: string;
}

const deleteReasonSchema = z.object({
  reason: z.string().min(5, "Reason must be at least 5 characters.").max(200),
});
type DeleteReasonFormValues = z.infer<typeof deleteReasonSchema>;


export default function AdminCategoriesPage() {
  const { categories, currentUser, addCategory, updateCategory, deleteCategory, allItems, users, updateAnyWishlistItem, deleteAnyWishlistItemByAdmin, refreshAllDataFromFirestore } = useWishlist();
  const { toast } = useToast();

  const [isCategoryFormDialogOpen, setIsCategoryFormDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);

  const [isItemFormDialogOpen, setIsItemFormDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<WishlistItemType | null>(null);
  // itemOwnerIdForEdit is still useful for local permission checks, even if not passed to context
  const [itemOwnerIdForEdit, setItemOwnerIdForEdit] = useState<string | null>(null);
  const [itemToDeleteForAdmin, setItemToDeleteForAdmin] = useState<EnrichedWishlistItem | null>(null);
  const [isItemDeleteConfirmOpen, setIsItemDeleteConfirmOpen] = useState(false);


  const enrichedAllItems: EnrichedWishlistItem[] = useMemo(() => {
    return allItems.map(item => {
      const owner = users.find(u => u.id === item.ownerUid); // Use ownerUid
      return {
        ...item,
        ownerName: owner?.name || 'Unknown Owner'
      };
    });
  }, [allItems, users]);


  const categoryForm = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: { title: "" },
  });

  const deleteReasonForm = useForm<DeleteReasonFormValues>({
    resolver: zodResolver(deleteReasonSchema),
    defaultValues: { reason: "" },
  });

  useEffect(() => {
    if (isCategoryFormDialogOpen) {
      if (editingCategory) {
        categoryForm.reset({ title: editingCategory.title });
      } else {
        categoryForm.reset({ title: "" });
      }
    }
  }, [isCategoryFormDialogOpen, editingCategory, categoryForm]);

  useEffect(() => {
    if (!isItemFormDialogOpen) {
        setEditingItem(null);
        setItemOwnerIdForEdit(null);
    }
  }, [isItemFormDialogOpen]);


  async function onCategorySubmit(values: CategoryFormValues) {
    let success = false;
    if (editingCategory) {
      success = await updateCategory(editingCategory.id, values);
      if (success) {
        toast({ title: "Category Updated", description: `"${values.title}" has been updated.` });
      } else {
        toast({ title: "Failed to Update", description: "Could not update the category.", variant: "destructive" });
      }
    } else {
      success = await addCategory({ title: values.title });
      if (success) {
        toast({ title: "Category Added", description: `"${values.title}" has been added.` });
      } else {
        toast({ title: "Failed to Add", description: "Could not add the category.", variant: "destructive" });
      }
    }
    if (success) {
      setIsCategoryFormDialogOpen(false);
      setEditingCategory(null);
      await refreshAllDataFromFirestore();
    }
  }

  const handleOpenAddCategoryDialog = () => {
    setEditingCategory(null);
    categoryForm.reset({ title: "" });
    setIsCategoryFormDialogOpen(true);
  };

  const handleOpenEditCategoryDialog = (category: Category) => {
    setEditingCategory(category);
    categoryForm.reset({ title: category.title });
    setIsCategoryFormDialogOpen(true);
  };

  const handleDeleteAttempt = async (category: Category) => {
    const result = await deleteCategory(category.id);
    if (result.success) {
      setCategoryToDelete(category);
    } else {
      toast({
        title: "Cannot Delete Category",
        description: result.message || "This category cannot be deleted.",
        variant: "destructive",
        duration: 5000,
      });
      setCategoryToDelete(null);
    }
  };

  const handleConfirmCategoryDelete = async () => {
    if (categoryToDelete) {
      const result = await deleteCategory(categoryToDelete.id);
      if (result.success) {
        toast({ title: "Category Deleted", description: `"${categoryToDelete.title}" has been deleted.` });
        await refreshAllDataFromFirestore();
      } else {
        toast({ title: "Delete Failed", description: result.message || "Could not delete the category.", variant: "destructive" });
      }
      setCategoryToDelete(null);
    }
  };

  const getItemsForCategory = useCallback((categoryId: string) => {
    return enrichedAllItems.filter(item => item.categoryId === categoryId);
  }, [enrichedAllItems]);


  const handleOpenEditItemDialog = (item: WishlistItemType) => {
    setEditingItem(item);
    setItemOwnerIdForEdit(item.ownerUid); // Keep for local permission checks if needed
    setIsItemFormDialogOpen(true);
  };

  const handleItemFormDialogSubmit = async () => {
    setIsItemFormDialogOpen(false);
    setEditingItem(null);
    setItemOwnerIdForEdit(null);
    await refreshAllDataFromFirestore();
  };

  const handleAdminUpdateItem = async (updatedItemData: Partial<Omit<WishlistItemType, 'id' | 'ownerUid' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => {
    if (!editingItem || !currentUser?.isAdmin) { // itemOwnerIdForEdit check can be removed from here if not strictly needed before context call
      toast({ title: "Update Failed", description: "Could not update item. Admin permissions required or item details missing.", variant: "destructive" });
      return;
    }
    // Call context function with itemId and updatedItemData
    const success = await updateAnyWishlistItem(editingItem.id, updatedItemData);
    if (success) {
      toast({ title: "Item Updated", description: `"${updatedItemData.name || editingItem.name}" has been updated by admin.` });
      handleItemFormDialogSubmit(); // This will now also call refreshAllDataFromFirestore
    } else {
      toast({ title: "Update Failed", description: "Could not update the item.", variant: "destructive" });
    }
  };

  const openItemDeleteConfirmation = (item: EnrichedWishlistItem) => {
    setItemToDeleteForAdmin(item);
    deleteReasonForm.reset({ reason: "" });
    setIsItemDeleteConfirmOpen(true);
  };

  const handleConfirmItemDelete = async (values: DeleteReasonFormValues) => {
    if (itemToDeleteForAdmin && currentUser?.isAdmin) {
      // Call context function with itemToDeleteForAdmin.id and reason
      const success = await deleteAnyWishlistItemByAdmin(itemToDeleteForAdmin.id, values.reason);
      if (success) {
        toast({ title: "Item Deleted", description: `"${itemToDeleteForAdmin.name}" has been deleted by admin.` });
        await refreshAllDataFromFirestore();
      } else {
        toast({ title: "Delete Failed", description: "Could not delete the item.", variant: "destructive" });
      }
      setIsItemDeleteConfirmOpen(false);
      setItemToDeleteForAdmin(null);
    }
  };


  if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manage Categories</h1>
          <p className="text-muted-foreground">Define and manage item categories for wishlists.</p>
        </div>
        <Dialog open={isCategoryFormDialogOpen} onOpenChange={(isOpen) => {
            setIsCategoryFormDialogOpen(isOpen);
            if (!isOpen) setEditingCategory(null);
          }}>
            <DialogTrigger asChild>
              <Button onClick={handleOpenAddCategoryDialog}>
                <PlusCircle className="mr-2 h-4 w-4" /> Add New Category
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingCategory ? "Edit Category" : "Add New Category"}</DialogTitle>
                <DialogDescriptionComponent>
                  {editingCategory ? "Update the title for this category." : "Enter the title for the new category."}
                </DialogDescriptionComponent>
              </DialogHeader>
              <Form {...categoryForm}>
                <form onSubmit={categoryForm.handleSubmit(onCategorySubmit)} className="space-y-4">
                  <FormField
                    control={categoryForm.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Title</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Electronics" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end gap-2">
                     <DialogClose asChild>
                      <Button type="button" variant="outline">Cancel</Button>
                    </DialogClose>
                    <Button type="submit">{editingCategory ? "Save Changes" : "Add Category"}</Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
      </div>

       <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
        <ShieldAlert className="h-5 w-5 !text-yellow-600" />
        <AlertTitle className="font-semibold">Data Management</AlertTitle>
        <AlertDescription>
          Category management now interacts with Firestore. Deletion is prevented if items use the category.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Category List & Associated Items</CardTitle>
          <CardDescription>Expand categories to see items using that category.</CardDescription>
        </CardHeader>
        <CardContent>
          {categories.length > 0 ? (
            <Accordion type="multiple" className="space-y-2">
              {categories.map((category) => {
                const itemsInCategory = getItemsForCategory(category.id);
                return (
                  <AccordionItem key={category.id} value={category.id} className="border rounded-md px-2">
                    <AccordionTrigger className="py-3 hover:no-underline">
                      <div className="flex justify-between items-center w-full pr-2">
                        <span className="font-medium text-base">{category.title} <Badge variant="secondary" className="ml-2">{itemsInCategory.length} item(s)</Badge></span>
                        <span className="text-xs text-muted-foreground">ID: {category.id}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="pt-2 pb-3 space-y-4">
                        <div className="flex items-center gap-2 border-b pb-3 mb-3">
                            <h4 className="text-sm font-semibold text-muted-foreground">Manage Category: "{category.title}"</h4>
                            <Button variant="outline" size="sm" onClick={() => handleOpenEditCategoryDialog(category)}>
                                <Edit className="mr-1.5 h-3.5 w-3.5" /> Edit Category Name
                            </Button>
                            <AlertDialog open={!!categoryToDelete && categoryToDelete.id === category.id} onOpenChange={(isOpen) => { if (!isOpen) setCategoryToDelete(null); }}>
                                <AlertDialogTriggerComponent asChild>
                                <Button variant="destructive" size="sm" onClick={() => handleDeleteAttempt(category)}>
                                    <Trash2 className="mr-1.5 h-3.5 w-3.5" /> Delete Category
                                </Button>
                                </AlertDialogTriggerComponent>
                                {categoryToDelete && categoryToDelete.id === category.id && (
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                    <AlertDialogTitleComponent>Are you sure?</AlertDialogTitleComponent>
                                    <AlertDialogDescriptionDialog>
                                        This action cannot be undone. This will permanently delete the category "{categoryToDelete.title}" from Firestore. If items are using this category, deletion may be prevented.
                                    </AlertDialogDescriptionDialog>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                    <AlertDialogCancel onClick={() => setCategoryToDelete(null)}>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={handleConfirmCategoryDelete}>Delete</AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                                )}
                            </AlertDialog>
                        </div>

                      {itemsInCategory.length > 0 ? (
                        <div className="max-h-96 overflow-y-auto">
                          <Table>
                            <TableHeader>
                              <TableRow><TableHead className="w-[60px]">Img</TableHead><TableHead>Item Name</TableHead><TableHead>Owner</TableHead><TableHead>Price</TableHead><TableHead className="text-center">Qty (D/R/P/Rec)</TableHead><TableHead className="text-right">Actions</TableHead></TableRow>
                            </TableHeader>
                            <TableBody>
                              {itemsInCategory.map((item) => {
                                const totalReserved = getTotalCommittedQuantity(item, 'reserved');
                                const totalPurchased = getTotalCommittedQuantity(item, 'purchased');
                                return (
                                <TableRow key={item.id}>
                                  <TableCell>
                                    {item.imageUrl ? (
                                      <Image src={item.imageUrl} alt={item.name} width={32} height={32} className="rounded object-cover aspect-square" data-ai-hint="product thumbnail" />
                                    ) : (
                                      <div className="w-8 h-8 bg-muted rounded flex items-center justify-center text-xs text-muted-foreground">No Img</div>
                                    )}
                                  </TableCell>
                                  <TableCell className="font-medium">{item.name}</TableCell>
                                  <TableCell className="text-muted-foreground">{item.ownerName}</TableCell>
                                  <TableCell>${item.price.toFixed(2)}</TableCell>
                                  <TableCell className="text-center">
                                    <Badge variant="outline" className="mr-1">D: {item.quantity}</Badge>
                                    {totalReserved > 0 && <Badge variant="secondary" className="mr-1">R: {totalReserved}</Badge>}
                                    {totalPurchased > 0 && <Badge variant="default" className="bg-accent text-accent-foreground mr-1">P: {totalPurchased}</Badge>}
                                    {item.receivedQuantity > 0 && <Badge variant="default" className="bg-green-600 text-white">Rec: {item.receivedQuantity}</Badge>}
                                  </TableCell>
                                  <TableCell className="text-right space-x-2">
                                    <Button variant="outline" size="icon" onClick={() => handleOpenEditItemDialog(item)}>
                                      <Edit className="h-4 w-4" />
                                      <span className="sr-only">Edit Item</span>
                                    </Button>
                                    <AlertDialog open={isItemDeleteConfirmOpen && itemToDeleteForAdmin?.id === item.id} onOpenChange={(isOpen) => {
                                      if (!isOpen) setItemToDeleteForAdmin(null);
                                      setIsItemDeleteConfirmOpen(isOpen);
                                    }}>
                                      <AlertDialogTriggerComponent asChild>
                                        <Button variant="destructive" size="icon" onClick={() => openItemDeleteConfirmation(item)}>
                                          <Trash2 className="h-4 w-4" />
                                          <span className="sr-only">Delete Item</span>
                                        </Button>
                                      </AlertDialogTriggerComponent>
                                      {itemToDeleteForAdmin && itemToDeleteForAdmin.id === item.id && (
                                        <AlertDialogContent>
                                          <AlertDialogHeader>
                                            <AlertDialogTitleComponent>Delete Item: {itemToDeleteForAdmin?.name}</AlertDialogTitleComponent>
                                            <AlertDialogDescriptionDialog>
                                              Are you sure you want to delete this item? This action cannot be undone. Please provide a reason for deletion.
                                            </AlertDialogDescriptionDialog>
                                          </AlertDialogHeader>
                                          <Form {...deleteReasonForm}>
                                            <form onSubmit={deleteReasonForm.handleSubmit(handleConfirmItemDelete)} className="space-y-4">
                                              <FormField
                                                control={deleteReasonForm.control}
                                                name="reason"
                                                render={({ field }) => (
                                                  <FormItem>
                                                    <FormLabel>Reason for Deletion</FormLabel>
                                                    <FormControl>
                                                      <Textarea placeholder="e.g., Item no longer available." {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                  </FormItem>
                                                )}
                                              />
                                              <AlertDialogFooter>
                                                <AlertDialogCancel onClick={() => {setIsItemDeleteConfirmOpen(false); setItemToDeleteForAdmin(null);}}>Cancel</AlertDialogCancel>
                                                <Button type="submit" variant="destructive">Confirm Delete</Button>
                                              </AlertDialogFooter>
                                            </form>
                                          </Form>
                                        </AlertDialogContent>
                                      )}
                                    </AlertDialog>
                                  </TableCell>
                                </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No items currently use this category.</p>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                )
              })}
            </Accordion>
          ) : (
             <p className="text-muted-foreground">No categories defined yet.</p>
          )}
        </CardContent>
      </Card>

      <Dialog open={isItemFormDialogOpen} onOpenChange={setIsItemFormDialogOpen}>
        <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Wishlist Item (Admin)</DialogTitle>
            <DialogDescriptionComponent>
              Update the details for this wishlist item. Changes will be attributed to admin.
            </DialogDescriptionComponent>
          </DialogHeader>
          {editingItem && (
            <AddWishlistItemForm
              itemToEdit={editingItem}
              onFormSubmit={handleItemFormDialogSubmit}
              onAdminSubmit={handleAdminUpdateItem}
              isAdminEdit={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

    