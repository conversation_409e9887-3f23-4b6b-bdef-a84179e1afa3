"use client";

import { useState, useEffect, useMemo } from 'react';
import { useWishlist } from '@/contexts/WishlistContext';
import type { Event as EventType, User, Group } from '@/lib/types'; // Renamed Event to EventType
import { AddEventForm } from '@/components/events/AddEventForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PlusCircle, Edit, Trash2, ShieldAlert, CalendarCog } from 'lucide-react';
import Link from 'next/link';
import { Alert, AlertDescription as AlertDescriptionComponent, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription as DialogDescriptionComponentDialog,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription as AlertDialogDescriptionDialogComponent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle as AlertDialogTitleComponent,
  AlertDialogTrigger, // Added AlertDialogTrigger
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import { format, parse } from 'date-fns';
import { Badge } from '@/components/ui/badge';

export default function AdminManageEventsPage() {
  const { 
    events, 
    currentUser, 
    users, 
    groupsData,
    addAdminEvent,
    updateAdminEvent,
    deleteAdminEvent 
  } = useWishlist();
  const { toast } = useToast();

  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<EventType | null>(null);
  const [eventToDelete, setEventToDelete] = useState<EventType | null>(null);

  const sortedEvents = useMemo(() => {
    return [...events].sort((a, b) => {
      const dateA = a.isRecurringYearly ? parse(`2000-${a.date}`, 'yyyy-MM-dd', new Date()) : parse(a.date, 'yyyy-MM-dd', new Date());
      const dateB = b.isRecurringYearly ? parse(`2000-${b.date}`, 'yyyy-MM-dd', new Date()) : parse(b.date, 'yyyy-MM-dd', new Date());
      return dateA.getTime() - dateB.getTime();
    });
  }, [events]);

  const handleOpenAddDialog = () => {
    setEditingEvent(null);
    setIsFormDialogOpen(true);
  };

  const handleOpenEditDialog = (event: EventType) => {
    setEditingEvent(event);
    setIsFormDialogOpen(true);
  };

  const handleFormSubmit = () => {
    setIsFormDialogOpen(false);
    setEditingEvent(null);
  };

  const handleConfirmDelete = () => {
    if (eventToDelete) {
      const success = deleteAdminEvent(eventToDelete.id);
      if (success) {
        toast({ title: "Event Deleted", description: `"${eventToDelete.name}" has been deleted.` });
      } else {
        toast({ title: "Delete Failed", description: "Could not delete the event.", variant: "destructive" });
      }
      setEventToDelete(null);
    }
  };

  const getFormattedEventDate = (event: EventType) => {
    if (event.isRecurringYearly) {
      try {
        return `${format(parse(event.date, 'MM-dd', new Date()), 'MMMM d')} (Recurring)`;
      } catch { return "Invalid Date (Recurring)"}
    }
    try {
     return format(parse(event.date, 'yyyy-MM-dd', new Date()), 'MMMM d, yyyy');
    } catch { return "Invalid Date"}
  };

  const getGroupNames = (groupIds: string[]) => {
    if (!groupIds || groupIds.length === 0) return 'N/A';
    return groupIds.map(id => groupsData.find(g => g.id === id)?.name || 'Unknown Group').join(', ');
  };

  const getCreatorName = (creatorId: string) => {
    if (creatorId === 'system') return 'System';
    return users.find(u => u.id === creatorId)?.name || 'Unknown User';
  };


  if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manage Events</h1>
          <p className="text-muted-foreground">Create, edit, or delete system-wide and group-specific events.</p>
        </div>
        <Dialog open={isFormDialogOpen} onOpenChange={setIsFormDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleOpenAddDialog}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add New Event
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingEvent ? 'Edit Event' : 'Add New Event'}</DialogTitle>
              <DialogDescriptionComponentDialog>
                {editingEvent ? 'Update the details for this event.' : 'Define a new event for the system or specific groups.'}
              </DialogDescriptionComponentDialog>
            </DialogHeader>
            <AddEventForm
              onFormSubmit={handleFormSubmit}
              isAdminForm={true}
              eventToEdit={editingEvent}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
        <ShieldAlert className="h-5 w-5 !text-yellow-600" />
        <AlertTitle className="font-semibold">Development Notice</AlertTitle>
        <AlertDescriptionComponent>
          Event management now interacts with Firestore. Deletion is prevented if items use the rank.
        </AlertDescriptionComponent>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Event List</CardTitle>
          <CardDescription>A list of all defined events, sorted by date.</CardDescription>
        </CardHeader>
        <CardContent>
          {sortedEvents.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Event Name</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Associated Groups</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedEvents.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell className="font-medium">{event.name}</TableCell>
                    <TableCell>{getFormattedEventDate(event)}</TableCell>
                    <TableCell>
                      <Badge variant={event.type === 'system' ? 'secondary' : 'outline'}>
                        {event.type === 'system' ? 'System' : 'User'}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-xs truncate" title={getGroupNames(event.associatedGroupIds)}>
                      {getGroupNames(event.associatedGroupIds)}
                    </TableCell>
                    <TableCell>{getCreatorName(event.createdByUserId)}</TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button variant="outline" size="icon" onClick={() => handleOpenEditDialog(event)}>
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit Event</span>
                      </Button>
                      <AlertDialog open={!!eventToDelete && eventToDelete.id === event.id} onOpenChange={(isOpen) => { if(!isOpen) setEventToDelete(null);}}>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="icon" onClick={() => setEventToDelete(event)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete Event</span>
                          </Button>
                        </AlertDialogTrigger>
                        {eventToDelete && eventToDelete.id === event.id && (
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitleComponent>Are you sure?</AlertDialogTitleComponent>
                            <AlertDialogDescriptionDialogComponent>
                              This action cannot be undone. This will permanently delete the event "{eventToDelete.name}".
                            </AlertDialogDescriptionDialogComponent>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setEventToDelete(null)}>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleConfirmDelete}>Delete</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                        )}
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-muted-foreground">No events defined yet.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
