
"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  useWishlist,
  getTotalCommittedQuantity // Import directly
} from '@/contexts/WishlistContext';
import type { WishlistItem, User, Category, Rank } from '@/lib/types';
import { AddWishlistItemForm } from '@/components/wishlists/AddWishlistItemForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Edit, ShieldAlert, Trash2, Search, ArrowUpDown, ArrowUp, ArrowDown, ExternalLink, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription as DialogDescriptionComponent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription as AlertDialogDescriptionDialog,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle as AlertDialogTitleComponent,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import Image from 'next/image';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface EnrichedWishlistItem extends WishlistItem {
  owner: User;
  categoryTitle?: string;
  rankTitle?: string;
}

const deleteReasonSchema = z.object({
  reason: z.string().min(5, "Reason must be at least 5 characters.").max(200),
});
type DeleteReasonFormValues = z.infer<typeof deleteReasonSchema>;

const deleteAllItemsConfirmationSchema = z.object({
  confirmationText: z.string().refine(value => value === "DELETE", {
    message: "You must type DELETE to confirm.",
  }),
});
type DeleteAllItemsConfirmationValues = z.infer<typeof deleteAllItemsConfirmationSchema>;


type SortableKeys = 'name' | 'price' | 'retailer' | 'quantity' | 'ownerName' | 'categoryTitle' | 'rankTitle';
type SortDirection = 'ascending' | 'descending';
interface SortConfig {
  key: SortableKeys;
  direction: SortDirection;
}

export default function AdminManageItemsPage() {
  const {
    users,
    categories,
    ranks,
    currentUser,
    updateAnyWishlistItem,
    deleteAnyWishlistItemByAdmin,
    deleteAllWishlistItemsFromAllUsers,
    allItems,
    refreshAllDataFromFirestore
  } = useWishlist();
  const { toast } = useToast();
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<WishlistItem | null>(null);
  // itemOwnerId is still useful for permission checks before calling context, even if not passed to context
  const [itemOwnerId, setItemOwnerId] = useState<string | null>(null); 
  const [itemToDelete, setItemToDelete] = useState<EnrichedWishlistItem | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);

  const [isDeleteAllConfirmDialogOpen, setIsDeleteAllConfirmDialogOpen] = useState(false);

  const deleteReasonForm = useForm<DeleteReasonFormValues>({
    resolver: zodResolver(deleteReasonSchema),
    defaultValues: { reason: "" },
  });

  const deleteAllItemsForm = useForm<DeleteAllItemsConfirmationValues>({
    resolver: zodResolver(deleteAllItemsConfirmationSchema),
    defaultValues: { confirmationText: "" },
  });

  const enrichedItems: EnrichedWishlistItem[] = useMemo(() => {
    return allItems.map(item => {
      const owner = users.find(u => u.id === item.ownerUid);
      return {
        ...item,
        owner: owner || { id: item.ownerUid, name: 'Unknown Owner', email: '', allowEmails: false, displayEmailToGroupMembers: false, isApproved: false, isAdmin: false, groups: [] },
        categoryTitle: item.categoryId ? categories.find(c => c.id === item.categoryId)?.title : undefined,
        rankTitle: item.rankId ? ranks.find(r => r.id === item.rankId)?.title : undefined,
      };
    });
  }, [allItems, users, categories, ranks]);

  const getInitials = (name?: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length === 1) return names[0][0] ? names[0][0].toUpperCase() : '';
    return (names[0][0] ? names[0][0].toUpperCase() : '') + (names[names.length - 1][0] ? names[names.length - 1][0].toUpperCase() : '');
  }

  const filteredItems = useMemo(() => {
    if (!searchTerm) {
      return enrichedItems;
    }
    const lowercasedSearchTerm = searchTerm.toLowerCase();
    return enrichedItems.filter(item =>
      item.name.toLowerCase().includes(lowercasedSearchTerm) ||
      item.owner.name.toLowerCase().includes(lowercasedSearchTerm) ||
      (item.retailer && item.retailer.toLowerCase().includes(lowercasedSearchTerm)) ||
      (item.categoryTitle && item.categoryTitle.toLowerCase().includes(lowercasedSearchTerm)) ||
      (item.rankTitle && item.rankTitle.toLowerCase().includes(lowercasedSearchTerm))
    );
  }, [enrichedItems, searchTerm]);

  const sortedItems = useMemo(() => {
    let sortableItems = [...filteredItems];
    if (sortConfig !== null) {
      sortableItems.sort((a, b) => {
        let valA, valB;
        if (sortConfig.key === 'ownerName') {
          valA = a.owner.name;
          valB = b.owner.name;
        } else if (sortConfig.key === 'categoryTitle') {
          valA = a.categoryTitle;
          valB = b.categoryTitle;
        } else if (sortConfig.key === 'rankTitle') {
          valA = a.rankTitle;
          valB = b.rankTitle;
        } else {
          valA = a[sortConfig.key as keyof WishlistItem];
          valB = b[sortConfig.key as keyof WishlistItem];
        }

        if (valA == null && valB == null) return 0;
        if (valA == null) return sortConfig.direction === 'ascending' ? 1 : -1;
        if (valB == null) return sortConfig.direction === 'ascending' ? -1 : 1;

        if (typeof valA === 'number' && typeof valB === 'number') {
          return sortConfig.direction === 'ascending' ? valA - valB : valB - valA;
        }
        if (typeof valA === 'string' && typeof valB === 'string') {
          const comparison = valA.toLowerCase().localeCompare(valB.toLowerCase());
          return sortConfig.direction === 'ascending' ? comparison : -comparison;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [filteredItems, sortConfig]);

  const requestSort = (key: SortableKeys) => {
    let direction: SortDirection = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const getSortIcon = (columnKey: SortableKeys) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <ArrowUpDown className="ml-2 h-3 w-3 opacity-50 group-hover:opacity-100 transition-opacity" />;
    }
    return sortConfig.direction === 'ascending' ? <ArrowUp className="ml-2 h-3 w-3" /> : <ArrowDown className="ml-2 h-3 w-3" />;
  };


  const handleOpenEditDialog = (item: WishlistItem) => {
    setEditingItem(item);
    setItemOwnerId(item.ownerUid); // Keep for local checks if needed, but not passed to context
    setIsFormDialogOpen(true);
  };

  const handleFormSubmit = async () => {
    setIsFormDialogOpen(false);
    setEditingItem(null);
    setItemOwnerId(null);
    await refreshAllDataFromFirestore();
  };

  const handleAdminUpdateItem = async (updatedItemData: Partial<Omit<WishlistItem, 'id' | 'ownerUid' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => {
    if (!editingItem || !currentUser?.isAdmin) { // itemOwnerId check can be removed or kept as local guard
      toast({ title: "Update Failed", description: "Could not update item. Admin permissions required or item details missing.", variant: "destructive" });
      return false;
    }
    // Call context function with itemId and updatedItemData
    const success = await updateAnyWishlistItem(editingItem.id, updatedItemData);
    if (success) {
      toast({ title: "Item Updated", description: `"${updatedItemData.name || editingItem.name}" has been updated by admin.` });
      handleFormSubmit();
      return true;
    } else {
      toast({ title: "Update Failed", description: "Could not update the item.", variant: "destructive" });
      return false;
    }
  };

  const openDeleteConfirmation = (item: EnrichedWishlistItem) => {
    setItemToDelete(item);
    deleteReasonForm.reset({ reason: "" });
    setIsDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async (values: DeleteReasonFormValues) => {
    if (itemToDelete && currentUser?.isAdmin) {
      // Call context function with itemToDelete.id and reason
      const success = await deleteAnyWishlistItemByAdmin(itemToDelete.id, values.reason);
      if (success) {
        toast({ title: "Item Deleted", description: `"${itemToDelete.name}" has been deleted by admin.` });
        await refreshAllDataFromFirestore();
      } else {
        toast({ title: "Delete Failed", description: "Could not delete the item.", variant: "destructive" });
      }
      setIsDeleteConfirmOpen(false);
      setItemToDelete(null);
    }
  };

  const handleConfirmDeleteAllItems = async () => {
    const success = await deleteAllWishlistItemsFromAllUsers();
    if (success) {
      toast({ title: "All Items Deleted", description: "All wishlist items have been cleared from the system." });
      await refreshAllDataFromFirestore();
    } else {
      toast({ title: "Operation Failed", description: "Could not delete all wishlist items.", variant: "destructive" });
    }
    setIsDeleteAllConfirmDialogOpen(false);
    deleteAllItemsForm.reset({ confirmationText: "" });
  };


  if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manage All Wishlist Items</h1>
          <p className="text-muted-foreground">View, search, sort, and edit any wishlist item in the system.</p>
        </div>
        <AlertDialog open={isDeleteAllConfirmDialogOpen} onOpenChange={setIsDeleteAllConfirmDialogOpen}>
          <AlertDialogTrigger asChild>
            <Button variant="destructive">
              <AlertTriangle className="mr-2 h-4 w-4" /> Delete All Items
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitleComponent>Confirm Deletion of ALL Wishlist Items</AlertDialogTitleComponent>
              <AlertDialogDescriptionDialog>
                This action is irreversible and will clear all items from every user's wishlist. Users will retain their wishlists, but they will be empty.
                To proceed, type "DELETE" in the box below.
              </AlertDialogDescriptionDialog>
            </AlertDialogHeader>
            <Form {...deleteAllItemsForm}>
              <form onSubmit={deleteAllItemsForm.handleSubmit(handleConfirmDeleteAllItems)} className="space-y-4">
                <FormField
                  control={deleteAllItemsForm.control}
                  name="confirmationText"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirmation</FormLabel>
                      <FormControl>
                        <Input placeholder='Type DELETE to confirm' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => deleteAllItemsForm.reset({ confirmationText: "" })}>Cancel</AlertDialogCancel>
                  <Button
                    type="submit"
                    variant="destructive"
                    disabled={deleteAllItemsForm.watch("confirmationText") !== "DELETE"}
                  >
                    Confirm Delete All
                  </Button>
                </AlertDialogFooter>
              </form>
            </Form>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
        <ShieldAlert className="h-5 w-5 !text-yellow-600" />
        <AlertTitle className="font-semibold">Data Management</AlertTitle>
        <AlertDescription>
          Item management interacts with Firestore. Edit and delete operations will update data in Firestore.
        </AlertDescription>
      </Alert>

      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search items by name, owner, retailer, category, rank..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8 w-full"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Items List</CardTitle>
          <CardDescription>
            {searchTerm ? `Showing ${sortedItems.length} of ${enrichedItems.length} items matching "${searchTerm}".` : `A list of all ${enrichedItems.length} wishlist items across all users.`}
            {sortConfig && ` Sorted by ${sortConfig.key} (${sortConfig.direction}).`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {sortedItems.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">Image</TableHead>
                    <TableHead className="cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('name')}>
                      <div className="flex items-center">Item Name {getSortIcon('name')}</div>
                    </TableHead>
                    <TableHead className="cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('ownerName')}>
                      <div className="flex items-center">Owner {getSortIcon('ownerName')}</div>
                    </TableHead>
                    <TableHead className="cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('price')}>
                      <div className="flex items-center">Price {getSortIcon('price')}</div>
                    </TableHead>
                    <TableHead className="cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('retailer')}>
                      <div className="flex items-center">Retailer {getSortIcon('retailer')}</div>
                    </TableHead>
                    <TableHead className="cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('categoryTitle')}>
                      <div className="flex items-center">Category {getSortIcon('categoryTitle')}</div>
                    </TableHead>
                    <TableHead className="cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('rankTitle')}>
                      <div className="flex items-center">Ranking {getSortIcon('rankTitle')}</div>
                    </TableHead>
                    <TableHead className="text-center cursor-pointer hover:bg-muted/50 group" onClick={() => requestSort('quantity')}>
                       <div className="flex items-center justify-center">Qty (D/R/P/Rec) {getSortIcon('quantity')}</div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedItems.map((item) => {
                    const totalReserved = getTotalCommittedQuantity(item, 'reserved');
                    const totalPurchased = getTotalCommittedQuantity(item, 'purchased');
                    return (
                      <TableRow key={item.id}>
                        <TableCell>
                          {item.imageUrl ? (
                            <Image src={item.imageUrl} alt={item.name} width={40} height={40} className="rounded object-cover aspect-square" data-ai-hint="product thumbnail" />
                          ) : (
                            <div className="w-10 h-10 bg-muted rounded flex items-center justify-center text-xs text-muted-foreground">No Img</div>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">
                          {item.url ? (
                            <a
                              href={item.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary hover:underline flex items-center gap-1"
                            >
                              {item.name}
                              <ExternalLink className="h-3 w-3 opacity-70" />
                            </a>
                          ) : (
                            item.name
                          )}
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={item.owner.avatarUrl} alt={item.owner.name} data-ai-hint="person avatar" />
                              <AvatarFallback>{getInitials(item.owner.name)}</AvatarFallback>
                            </Avatar>
                            <span>{item.owner.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>${item.price.toFixed(2)}</TableCell>
                        <TableCell className="text-muted-foreground truncate max-w-xs">{item.retailer}</TableCell>
                        <TableCell className="text-muted-foreground">{item.categoryTitle || 'N/A'}</TableCell>
                        <TableCell className="text-muted-foreground">{item.rankTitle || 'N/A'}</TableCell>
                        <TableCell className="text-center">
                          <Badge variant="outline" className="mr-1">D: {item.quantity}</Badge>
                          {totalReserved > 0 && <Badge variant="secondary" className="mr-1">R: {totalReserved}</Badge>}
                          {totalPurchased > 0 && <Badge variant="default" className="bg-accent text-accent-foreground mr-1">P: {totalPurchased}</Badge>}
                          {item.receivedQuantity > 0 && <Badge variant="default" className="bg-green-600 text-white">Rec: {item.receivedQuantity}</Badge>}
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button variant="outline" size="icon" onClick={() => handleOpenEditDialog(item)}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit Item</span>
                          </Button>
                          <AlertDialog open={isDeleteConfirmOpen && itemToDelete?.id === item.id} onOpenChange={(isOpen) => {
                            if (!isOpen) setItemToDelete(null);
                            setIsDeleteConfirmOpen(isOpen);
                          }}>
                            <AlertDialogTrigger asChild>
                              <Button variant="destructive" size="icon" onClick={() => openDeleteConfirmation(item)}>
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete Item</span>
                              </Button>
                            </AlertDialogTrigger>
                            {itemToDelete && itemToDelete.id === item.id && (
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitleComponent>Delete Item: {itemToDelete?.name}</AlertDialogTitleComponent>
                                  <AlertDialogDescriptionDialog>
                                    Are you sure you want to delete this item? This action cannot be undone. Please provide a reason for deletion.
                                  </AlertDialogDescriptionDialog>
                                </AlertDialogHeader>
                                <Form {...deleteReasonForm}>
                                  <form onSubmit={deleteReasonForm.handleSubmit(handleConfirmDelete)} className="space-y-4">
                                    <FormField
                                      control={deleteReasonForm.control}
                                      name="reason"
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormLabel>Reason for Deletion</FormLabel>
                                          <FormControl>
                                            <Textarea placeholder="e.g., Item no longer available, Inappropriate item." {...field} />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                    <AlertDialogFooter>
                                      <AlertDialogCancel onClick={() => {setIsDeleteConfirmOpen(false); setItemToDelete(null);}}>Cancel</AlertDialogCancel>
                                      <Button type="submit" variant="destructive">Confirm Delete</Button>
                                    </AlertDialogFooter>
                                  </form>
                                </Form>
                              </AlertDialogContent>
                            )}
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-muted-foreground">{searchTerm ? `No items found matching "${searchTerm}".` : "No items found in any wishlist."}</p>
          )}
        </CardContent>
      </Card>

      <Dialog open={isFormDialogOpen} onOpenChange={setIsFormDialogOpen}>
        <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Wishlist Item (Admin)</DialogTitle>
            <DialogDescriptionComponent>
              Update the details for this wishlist item. Changes will be attributed to admin.
            </DialogDescriptionComponent>
          </DialogHeader>
          {editingItem && (
            <AddWishlistItemForm
              itemToEdit={editingItem}
              onFormSubmit={handleFormSubmit}
              onAdminSubmit={handleAdminUpdateItem}
              isAdminEdit={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

    