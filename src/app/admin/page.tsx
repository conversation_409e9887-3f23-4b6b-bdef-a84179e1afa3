
"use client";

import Link from 'next/link';
import { useWishlist } from '@/contexts/WishlistContext';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Users2, Layers, BarChartBig, ShieldAlert, ArrowRight, ShoppingBasket, BellRing, CalendarCog, DatabaseZap, ListChecks, CheckCircle, AlertCircle, Info as InfoIcon, Loader2, Heart } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useState } from 'react';
import { collection, getDocs, limit, query, type DocumentData, type QuerySnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';


interface ValidationResult {
  collectionName: string;
  status: 'pending' | 'success' | 'warning' | 'error';
  message: string;
}

const collectionsToValidate = [
  { name: 'users', criticalFields: ['name', 'email', 'groups'] },
  { name: 'groups', criticalFields: ['name'] },
  { name: 'wishlists', criticalFields: ['userId'] }, // Updated: No longer checks for 'items' array
  { name: 'items', criticalFields: ['ownerUid', 'wishlistId', 'name', 'price', 'quantity', 'commitments'] }, // New: Added 'items' collection check
  { name: 'events', criticalFields: ['name', 'date', 'type'] },
  { name: 'categories', criticalFields: ['title'] },
  { name: 'ranks', criticalFields: ['title', 'order'] },
  { name: 'messages', criticalFields: ['recipientUid', 'content', 'timestamp'] },
];


export default function AdminDashboardPage() {
  const { currentUser } = useWishlist();
  const { toast } = useToast();
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [isPerformingValidation, setIsPerformingValidation] = useState(false);

  const handleRunValidationChecks = async () => {
    if (!db) {
      toast({ title: "Validation Error", description: "Firestore is not available.", variant: "destructive" });
      return;
    }
    setIsPerformingValidation(true);
    const initialResults: ValidationResult[] = collectionsToValidate.map(ctv => ({
      collectionName: ctv.name,
      status: 'pending',
      message: 'Checking...',
    }));
    setValidationResults(initialResults);

    const results: ValidationResult[] = [];

    for (const colToValidate of collectionsToValidate) {
      let result: ValidationResult = { collectionName: colToValidate.name, status: 'error', message: 'Unknown error' };
      try {
        const q = query(collection(db, colToValidate.name), limit(1));
        const snapshot: QuerySnapshot<DocumentData> = await getDocs(q);

        if (snapshot.empty) {
          result = { collectionName: colToValidate.name, status: 'warning', message: `Collection is empty or not found. Ensure it exists and contains data.` };
        } else {
          const docData = snapshot.docs[0].data();
          const missingFields = colToValidate.criticalFields.filter(field => docData[field] === undefined);

          if (missingFields.length > 0) {
            result = { collectionName: colToValidate.name, status: 'warning', message: `First document is missing critical fields: ${missingFields.join(', ')}.` };
          } else {
            // Basic type checks (can be expanded)
            let typeError = false;
            if (colToValidate.name === 'users' && !Array.isArray(docData.groups)) {
               result = { collectionName: colToValidate.name, status: 'warning', message: `Field 'groups' in first user document is not an array.` };
               typeError = true;
            }
            // Removed check for docData.items in 'wishlists' as it's no longer there.
            if (colToValidate.name === 'items' && !Array.isArray(docData.commitments)) {
               result = { collectionName: colToValidate.name, status: 'warning', message: `Field 'commitments' in first item document is not an array.` };
               typeError = true;
            }
             if (colToValidate.name === 'ranks' && typeof docData.order !== 'number') {
               result = { collectionName: colToValidate.name, status: 'warning', message: `Field 'order' in first rank document is not a number.` };
               typeError = true;
            }

            if (!typeError) {
                result = { collectionName: colToValidate.name, status: 'success', message: 'Collection accessible and first document seems to have key fields.' };
            }
          }
        }
      } catch (error: any) {
        console.error(`Error validating ${colToValidate.name}:`, error);
        if (error.code === 'permission-denied' || error.code === 'unimplemented' /* For missing indexes */) {
          result = { collectionName: colToValidate.name, status: 'error', message: 'Permission denied or required index missing. Check Firestore rules and indexes.' };
        } else {
          result = { collectionName: colToValidate.name, status: 'error', message: `Error querying collection: ${error.message}` };
        }
      }
      results.push(result);
      setValidationResults([...results, ...initialResults.slice(results.length)]); // Update incrementally
    }
    setIsPerformingValidation(false);
    toast({ title: "Validation Complete", description: "Firestore validation checks finished." });
  };


  if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  const adminSections = [
    { title: "Manage Users", description: "Add, edit, or remove users.", href: "/admin/users", icon: Users },
    { title: "Manage Groups", description: "Create, edit, or delete groups/families.", href: "/admin/groups", icon: Users2 },
    { title: "Manage Categories", description: "Define item categories for wishlists.", href: "/admin/categories", icon: Layers },
    { title: "Manage Ranks", description: "Set up item ranking options.", href: "/admin/ranks", icon: BarChartBig },
    { title: "Manage Items", description: "Edit or manage all wishlist items.", href: "/admin/items", icon: ShoppingBasket },
    { title: "Manage Wishlists", description: "View and manage all user wishlists.", href: "/admin/wishlists", icon: Heart },
    { title: "Manage Events", description: "Create, edit, or delete system/group events.", href: "/admin/events", icon: CalendarCog },
    { title: "Manage Notifications", description: "View and delete system notifications.", href: "/admin/notifications", icon: BellRing },
  ];

  return (
    <div className="space-y-8">
      <section>
        <h1 className="text-3xl font-bold tracking-tight mb-2">Admin Dashboard</h1>
        <p className="text-muted-foreground">Welcome, {currentUser.name}. Use the sections below to manage the GiftLink application.</p>
      </section>

       <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
        <ShieldAlert className="h-5 w-5 !text-yellow-600" />
        <AlertTitle className="font-semibold">Important Notice</AlertTitle>
        <AlertDescription>
          Admin operations now interact with your Firestore database. Changes are persistent. Ensure Firestore security rules are appropriately configured.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><DatabaseZap className="h-6 w-6 text-primary" />Firestore Data Validation</CardTitle>
          <CardDescription>Run basic checks on your Firestore collections to ensure they are accessible and structured as expected by the application.</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleRunValidationChecks} disabled={isPerformingValidation}>
            {isPerformingValidation ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <ListChecks className="mr-2 h-4 w-4" />
            )}
            Run Validation Checks
          </Button>
          {validationResults.length > 0 && (
            <div className="mt-4 space-y-3">
              <h3 className="text-md font-semibold">Validation Results:</h3>
              <ul className="space-y-2">
                {validationResults.map((result) => (
                  <li key={result.collectionName} className="p-3 border rounded-md">
                    <div className="flex items-center gap-2">
                      {result.status === 'pending' && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
                      {result.status === 'success' && <CheckCircle className="h-5 w-5 text-green-500" />}
                      {result.status === 'warning' && <AlertCircle className="h-5 w-5 text-yellow-500" />}
                      {result.status === 'error' && <InfoIcon className="h-5 w-5 text-red-500" />}
                      <span className="font-medium">{result.collectionName}</span>
                      <Badge variant={
                        result.status === 'success' ? 'default' :
                        result.status === 'warning' ? 'secondary' :
                        result.status === 'error' ? 'destructive' : 'outline'
                      } className="ml-auto text-xs capitalize">
                        {result.status}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">{result.message}</p>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>


      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminSections.map((section) => (
          <Card key={section.title} className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader>
              <div className="flex items-center gap-3 mb-2">
                <section.icon className="h-7 w-7 text-primary" />
                <CardTitle className="text-xl">{section.title}</CardTitle>
              </div>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="outline" className="w-full">
                <Link href={section.href}>
                  Go to {section.title} <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

