
"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  useWishlist,
  getTotalCommittedQuantity // Import directly
} from '@/contexts/WishlistContext';
import type { Rank, WishlistItem as WishlistItemType, User } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from '@/components/ui/badge';
import { PlusCircle, Edit, Trash2, ShieldAlert, Loader2 } from 'lucide-react'; // Added Loader2
import Link from 'next/link';
import Image from 'next/image';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription as DialogDescriptionComponent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription as AlertDialogDescriptionDialog,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle as AlertDialogTitleComponent,
  AlertDialogTrigger as AlertDialogTriggerComponent,
} from "@/components/ui/alert-dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from '@/hooks/use-toast';
import { AddWishlistItemForm } from '@/components/wishlists/AddWishlistItemForm';
import { SafeHtml } from '@/lib/sanitize';

const rankFormSchema = z.object({
  title: z.string().min(1, "Rank title is required.").max(50),
  description: z.string().max(200, "Description cannot exceed 200 characters.").optional(),
  order: z.coerce.number().int("Order must be an integer.").min(0, "Order must be 0 or greater."),
});
type RankFormValues = z.infer<typeof rankFormSchema>;

interface EnrichedWishlistItem extends WishlistItemType {
  ownerName: string;
}

const deleteReasonSchema = z.object({
  reason: z.string().min(5, "Reason must be at least 5 characters.").max(200),
});
type DeleteReasonFormValues = z.infer<typeof deleteReasonSchema>;


export default function AdminRanksPage() {
  const { ranks, currentUser, addRank, updateRank, deleteRank, allItems, users, updateAnyWishlistItem, deleteAnyWishlistItemByAdmin, refreshAllDataFromFirestore } = useWishlist();
  const { toast } = useToast();

  const [isRankFormDialogOpen, setIsRankFormDialogOpen] = useState(false);
  const [editingRank, setEditingRank] = useState<Rank | null>(null);
  const [rankToDelete, setRankToDelete] = useState<Rank | null>(null);


  const [isItemFormDialogOpen, setIsItemFormDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<WishlistItemType | null>(null);
  // itemOwnerIdForEdit is still useful for local permission checks, even if not passed to context
  const [itemOwnerIdForEdit, setItemOwnerIdForEdit] = useState<string | null>(null);
  const [itemToDeleteForAdmin, setItemToDeleteForAdmin] = useState<EnrichedWishlistItem | null>(null);
  const [isItemDeleteConfirmOpen, setIsItemDeleteConfirmOpen] = useState(false);

  const enrichedAllItems: EnrichedWishlistItem[] = useMemo(() => {
    return allItems.map(item => {
      const owner = users.find(u => u.id === item.ownerUid);
      return {
        ...item,
        ownerName: owner?.name || 'Unknown Owner'
      };
    });
  }, [allItems, users]);

  const rankForm = useForm<RankFormValues>({
    resolver: zodResolver(rankFormSchema),
    defaultValues: { title: "", description: "", order: 0 },
  });

  const deleteReasonForm = useForm<DeleteReasonFormValues>({
    resolver: zodResolver(deleteReasonSchema),
    defaultValues: { reason: "" },
  });


  useEffect(() => {
    const defaultOrder = ranks.length > 0 ? Math.max(0, ...ranks.map(r => r.order)) + 1 : 0;
    if (isRankFormDialogOpen) {
      if (editingRank) {
        rankForm.reset({
          title: editingRank.title,
          description: editingRank.description || "",
          order: editingRank.order
        });
      } else {
        rankForm.reset({ title: "", description: "", order: defaultOrder });
      }
    }
  }, [isRankFormDialogOpen, editingRank, rankForm, ranks]);

  useEffect(() => {
    if (!isItemFormDialogOpen) {
        setEditingItem(null);
        setItemOwnerIdForEdit(null);
    }
  }, [isItemFormDialogOpen]);


  async function onRankSubmit(values: RankFormValues) {
    let success = false;
    if (editingRank) {
      success = await updateRank(editingRank.id, values);
      if (success) {
        toast({ title: "Rank Updated", description: `"${values.title}" has been updated.` });
      } else {
        toast({ title: "Failed to Update", description: "Could not update the rank.", variant: "destructive" });
      }
    } else {
      success = await addRank({ title: values.title, description: values.description, order: values.order });
      if (success) {
        toast({ title: "Rank Added", description: `"${values.title}" has been added.` });
      } else {
        toast({ title: "Failed to Add", description: "Could not add the rank.", variant: "destructive" });
      }
    }

    if (success) {
      setIsRankFormDialogOpen(false);
      setEditingRank(null);
      await refreshAllDataFromFirestore();
    }
  }

  const handleOpenAddRankDialog = () => {
    setEditingRank(null);
    const defaultOrder = ranks.length > 0 ? Math.max(0, ...ranks.map(r => r.order)) + 1 : 0;
    rankForm.reset({ title: "", description: "", order: defaultOrder });
    setIsRankFormDialogOpen(true);
  };

  const handleOpenEditRankDialog = (rank: Rank) => {
    setEditingRank(rank);
    rankForm.reset({ title: rank.title, description: rank.description || "", order: rank.order });
    setIsRankFormDialogOpen(true);
  };

  const handleDeleteAttempt = async (rank: Rank) => {
    const result = await deleteRank(rank.id);
    if (result.success) {
        setRankToDelete(rank); // Proceed to show confirmation dialog
    } else {
        toast({
            title: "Cannot Delete Rank",
            description: result.message || "This rank cannot be deleted.",
            variant: "destructive",
            duration: 5000,
        });
        setRankToDelete(null); // Don't show dialog if pre-check fails
    }
  };

  const handleConfirmRankDelete = async () => {
    if (rankToDelete) {
      const result = await deleteRank(rankToDelete.id);
      if (result.success) {
        toast({ title: "Rank Deleted", description: `"${rankToDelete.title}" has been deleted.` });
        await refreshAllDataFromFirestore();
      } else {
        toast({ title: "Delete Failed", description: result.message || "Could not delete the rank.", variant: "destructive" });
      }
      setRankToDelete(null);
    }
  };

  const getItemsForRank = useCallback((rankId: string) => {
    return enrichedAllItems.filter(item => item.rankId === rankId);
  }, [enrichedAllItems]);


  const handleOpenEditItemDialog = (item: WishlistItemType) => {
    setEditingItem(item);
    setItemOwnerIdForEdit(item.ownerUid); // Keep for local permission checks if needed
    setIsItemFormDialogOpen(true);
  };

  const handleItemFormDialogSubmit = async () => {
    setIsItemFormDialogOpen(false);
    setEditingItem(null);
    setItemOwnerIdForEdit(null);
    await refreshAllDataFromFirestore();
  };

  const handleAdminUpdateItem = async (updatedItemData: Partial<Omit<WishlistItemType, 'id' | 'ownerUid' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => {
    if (!editingItem || !currentUser?.isAdmin) { // itemOwnerIdForEdit check can be removed
      toast({ title: "Update Failed", description: "Could not update item. Admin permissions required or item details missing.", variant: "destructive" });
      return;
    }
    // Call context function with itemId and updatedItemData
    const success = await updateAnyWishlistItem(editingItem.id, updatedItemData);
    if (success) {
      toast({ title: "Item Updated", description: `"${updatedItemData.name || editingItem.name}" has been updated by admin.` });
      handleItemFormDialogSubmit();
    } else {
      toast({ title: "Update Failed", description: "Could not update the item.", variant: "destructive" });
    }
  };

  const openItemDeleteConfirmation = (item: EnrichedWishlistItem) => {
    setItemToDeleteForAdmin(item);
    deleteReasonForm.reset({ reason: "" });
    setIsItemDeleteConfirmOpen(true);
  };

  const handleConfirmItemDelete = async (values: DeleteReasonFormValues) => {
    if (itemToDeleteForAdmin && currentUser?.isAdmin) {
      // Call context function with itemToDeleteForAdmin.id and reason
      const success = await deleteAnyWishlistItemByAdmin(itemToDeleteForAdmin.id, values.reason);
      if (success) {
        toast({ title: "Item Deleted", description: `"${itemToDeleteForAdmin.name}" has been deleted by admin.` });
        await refreshAllDataFromFirestore();
      } else {
        toast({ title: "Delete Failed", description: "Could not delete the item.", variant: "destructive" });
      }
      setIsItemDeleteConfirmOpen(false);
      setItemToDeleteForAdmin(null);
    }
  };


   if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manage Ranks</h1>
          <p className="text-muted-foreground">Define and manage item ranking options for wishlists.</p>
        </div>
        <Dialog open={isRankFormDialogOpen} onOpenChange={(isOpen) => {
          setIsRankFormDialogOpen(isOpen);
          if (!isOpen) setEditingRank(null);
        }}>
          <DialogTrigger asChild>
             <Button onClick={handleOpenAddRankDialog}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add New Rank
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>{editingRank ? "Edit Rank" : "Add New Rank"}</DialogTitle>
              <DialogDescriptionComponent>
                {editingRank ? "Update the details for this rank." : "Define a new ranking option for items."}
              </DialogDescriptionComponent>
            </DialogHeader>
            <Form {...rankForm}>
              <form onSubmit={rankForm.handleSubmit(onRankSubmit)} className="space-y-4">
                <FormField
                  control={rankForm.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rank Title</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., ⭐⭐⭐⭐⭐ or High Priority" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={rankForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Supports HTML. e.g., Most desired items. ✨" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={rankForm.control}
                  name="order"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Order</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="e.g., 0" {...field} />
                      </FormControl>
                      <FormDescription>Lower numbers appear first.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end gap-2">
                  <DialogClose asChild>
                     <Button type="button" variant="outline">Cancel</Button>
                  </DialogClose>
                  <Button type="submit">{editingRank ? "Save Changes" : "Add Rank"}</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
        <ShieldAlert className="h-5 w-5 !text-yellow-600" />
        <AlertTitle className="font-semibold">Data Management</AlertTitle>
        <AlertDescription>
          Rank management now interacts with Firestore. Deletion is prevented if items use the rank.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Rank List & Associated Items</CardTitle>
          <CardDescription>Expand ranks to see items using that rank. Sorted by order.</CardDescription>
        </CardHeader>
        <CardContent>
          {ranks.length > 0 ? (
             <Accordion type="multiple" className="space-y-2">
              {ranks.sort((a, b) => a.order - b.order).map((rank) => {
                const itemsInRank = getItemsForRank(rank.id);
                return (
                  <AccordionItem key={rank.id} value={rank.id} className="border rounded-md px-2">
                    <AccordionTrigger className="py-3 hover:no-underline">
                      <div className="flex justify-between items-center w-full pr-2">
                        <span className="font-medium text-base">{rank.title} <Badge variant="secondary" className="ml-2">{itemsInRank.length} item(s)</Badge></span>
                        <div className="flex items-center gap-4">
                            {rank.description && <SafeHtml html={rank.description} className="text-xs text-muted-foreground hidden md:inline" tag="span" />}
                            <span className="text-xs text-muted-foreground">Order: {rank.order}</span>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="pt-2 pb-3 space-y-4">
                        <div className="flex items-center gap-2 border-b pb-3 mb-3">
                            <h4 className="text-sm font-semibold text-muted-foreground">Manage Rank: "{rank.title}"</h4>
                            <Button variant="outline" size="sm" onClick={() => handleOpenEditRankDialog(rank)}>
                                <Edit className="mr-1.5 h-3.5 w-3.5" /> Edit Rank
                            </Button>
                            <AlertDialog open={!!rankToDelete && rankToDelete.id === rank.id} onOpenChange={(isOpen) => { if (!isOpen) setRankToDelete(null); }}>
                                <AlertDialogTriggerComponent asChild>
                                <Button variant="destructive" size="sm" onClick={() => handleDeleteAttempt(rank)}>
                                    <Trash2 className="mr-1.5 h-3.5 w-3.5" /> Delete Rank
                                </Button>
                                </AlertDialogTriggerComponent>
                                {rankToDelete && rankToDelete.id === rank.id && (
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                    <AlertDialogTitleComponent>Are you sure?</AlertDialogTitleComponent>
                                    <AlertDialogDescriptionDialog>
                                        This action cannot be undone. This will permanently delete the rank "{rankToDelete.title}" from Firestore. If items are using this rank, deletion may be prevented.
                                    </AlertDialogDescriptionDialog>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                    <AlertDialogCancel onClick={() => setRankToDelete(null)}>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={handleConfirmRankDelete}>Delete</AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                                )}
                            </AlertDialog>
                        </div>
                        {rank.description && <SafeHtml html={rank.description} className="text-sm text-muted-foreground md:hidden" tag="p" />}

                      {itemsInRank.length > 0 ? (
                        <div className="max-h-96 overflow-y-auto">
                          <Table>
                            <TableHeader>
                              <TableRow><TableHead className="w-[60px]">Img</TableHead><TableHead>Item Name</TableHead><TableHead>Owner</TableHead><TableHead>Price</TableHead><TableHead className="text-center">Qty (D/R/P/Rec)</TableHead><TableHead className="text-right">Actions</TableHead></TableRow>
                            </TableHeader>
                            <TableBody>
                              {itemsInRank.map((item) => {
                                 const totalReserved = getTotalCommittedQuantity(item, 'reserved');
                                 const totalPurchased = getTotalCommittedQuantity(item, 'purchased');
                                 return (
                                <TableRow key={item.id}>
                                   <TableCell>
                                    {item.imageUrl ? (
                                      <Image src={item.imageUrl} alt={item.name} width={32} height={32} className="rounded object-cover aspect-square" data-ai-hint="product thumbnail" />
                                    ) : (
                                      <div className="w-8 h-8 bg-muted rounded flex items-center justify-center text-xs text-muted-foreground">No Img</div>
                                    )}
                                  </TableCell>
                                  <TableCell className="font-medium">{item.name}</TableCell>
                                  <TableCell className="text-muted-foreground">{item.ownerName}</TableCell>
                                  <TableCell>${item.price.toFixed(2)}</TableCell>
                                   <TableCell className="text-center">
                                    <Badge variant="outline" className="mr-1">D: {item.quantity}</Badge>
                                    {totalReserved > 0 && <Badge variant="secondary" className="mr-1">R: {totalReserved}</Badge>}
                                    {totalPurchased > 0 && <Badge variant="default" className="bg-accent text-accent-foreground mr-1">P: {totalPurchased}</Badge>}
                                    {item.receivedQuantity > 0 && <Badge variant="default" className="bg-green-600 text-white">Rec: {item.receivedQuantity}</Badge>}
                                  </TableCell>
                                  <TableCell className="text-right space-x-2">
                                     <Button variant="outline" size="icon" onClick={() => handleOpenEditItemDialog(item)}>
                                      <Edit className="h-4 w-4" />
                                      <span className="sr-only">Edit Item</span>
                                    </Button>
                                    <AlertDialog open={isItemDeleteConfirmOpen && itemToDeleteForAdmin?.id === item.id} onOpenChange={(isOpen) => {
                                      if (!isOpen) setItemToDeleteForAdmin(null);
                                      setIsItemDeleteConfirmOpen(isOpen);
                                    }}>
                                      <AlertDialogTriggerComponent asChild>
                                        <Button variant="destructive" size="icon" onClick={() => openItemDeleteConfirmation(item)}>
                                          <Trash2 className="h-4 w-4" />
                                          <span className="sr-only">Delete Item</span>
                                        </Button>
                                      </AlertDialogTriggerComponent>
                                      {itemToDeleteForAdmin && itemToDeleteForAdmin.id === item.id && (
                                        <AlertDialogContent>
                                          <AlertDialogHeader>
                                            <AlertDialogTitleComponent>Delete Item: {itemToDeleteForAdmin?.name}</AlertDialogTitleComponent>
                                            <AlertDialogDescriptionDialog>
                                              Are you sure you want to delete this item? This action cannot be undone. Please provide a reason for deletion.
                                            </AlertDialogDescriptionDialog>
                                          </AlertDialogHeader>
                                          <Form {...deleteReasonForm}>
                                            <form onSubmit={deleteReasonForm.handleSubmit(handleConfirmItemDelete)} className="space-y-4">
                                              <FormField
                                                control={deleteReasonForm.control}
                                                name="reason"
                                                render={({ field }) => (
                                                  <FormItem>
                                                    <FormLabel>Reason for Deletion</FormLabel>
                                                    <FormControl>
                                                      <Textarea placeholder="e.g., Item no longer available." {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                  </FormItem>
                                                )}
                                              />
                                              <AlertDialogFooter>
                                                <AlertDialogCancel onClick={() => {setIsItemDeleteConfirmOpen(false); setItemToDeleteForAdmin(null);}}>Cancel</AlertDialogCancel>
                                                <Button type="submit" variant="destructive">Confirm Delete</Button>
                                              </AlertDialogFooter>
                                            </form>
                                          </Form>
                                        </AlertDialogContent>
                                      )}
                                    </AlertDialog>
                                  </TableCell>
                                </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No items currently use this rank.</p>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                )
              })}
            </Accordion>
          ) : (
            <p className="text-muted-foreground">No ranks defined yet.</p>
          )}
        </CardContent>
      </Card>

       <Dialog open={isItemFormDialogOpen} onOpenChange={setIsItemFormDialogOpen}>
        <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Wishlist Item (Admin)</DialogTitle>
            <DialogDescriptionComponent>
              Update the details for this wishlist item. Changes will be attributed to admin.
            </DialogDescriptionComponent>
          </DialogHeader>
          {editingItem && (
            <AddWishlistItemForm
              itemToEdit={editingItem}
              onFormSubmit={handleItemFormDialogSubmit}
              onAdminSubmit={handleAdminUpdateItem}
              isAdminEdit={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

    