
"use client";

import { useState, useEffect, useCallback } from 'react';
import { useWishlist } from '@/contexts/WishlistContext';
import type { User, Group } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { UserPlus, Edit, Trash2, ShieldAlert, CheckCircle2, XCircle, ShieldCheck, MailCheck, MailX, Eye, EyeOff, Info } from 'lucide-react';
import Link from 'next/link';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription as DialogDescriptionComponentDialog,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription as AlertDialogDescriptionDialogComponent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle as AlertDialogTitleComponent,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Form, FormControl, FormDescription as FormDescriptionForm, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from '@/hooks/use-toast';

const addUserFormSchema = z.object({
  name: z.string().min(2, "Full name must be at least 2 characters.").max(100),
  email: z.string().email("Invalid email address."),
  password: z.string().min(6, "Password must be at least 6 characters."),
  confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters."),
  allowEmails: z.boolean().default(true),
  displayEmailToGroupMembers: z.boolean().default(true),
  isApproved: z.boolean().default(true),
  isAdmin: z.boolean().default(false),
  groups: z.array(z.string()).refine(value => value.some(item => item), {
    message: "You have to select at least one group.",
  }),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});


const editUserFormSchema = z.object({
  name: z.string().min(2, "Full name must be at least 2 characters.").max(100),
  email: z.string().email("Invalid email address."),
  allowEmails: z.boolean().default(true),
  displayEmailToGroupMembers: z.boolean().default(true),
  isApproved: z.boolean().default(true),
  isAdmin: z.boolean().default(false),
  groups: z.array(z.string()).refine(value => value.some(item => item), {
    message: "You have to select at least one group.",
  }),
});

type AddUserFormValues = z.infer<typeof addUserFormSchema>;
type EditUserFormValues = z.infer<typeof editUserFormSchema>;


export default function AdminUsersPage() {
  const { users, currentUser, groupsData, addUser, updateUser, deleteUser, refreshAllDataFromFirestore } = useWishlist();
  const { toast } = useToast();
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  const form = useForm<AddUserFormValues | EditUserFormValues>({
    resolver: zodResolver(editingUser ? editUserFormSchema : addUserFormSchema),
    defaultValues: {
      name: "",
      email: "",
      // @ts-ignore
      password: "",
      // @ts-ignore
      confirmPassword: "",
      allowEmails: true,
      displayEmailToGroupMembers: true,
      isApproved: true,
      isAdmin: false,
      groups: [],
    },
  });

 useEffect(() => {
    form.reset(undefined, { keepValues: false, keepDirty: false, keepErrors: false });
    if (isFormDialogOpen) {
      if (editingUser) {
        form.setValue("name", editingUser.name);
        form.setValue("email", editingUser.email);
        form.setValue("allowEmails", editingUser.allowEmails);
        form.setValue("displayEmailToGroupMembers", editingUser.displayEmailToGroupMembers);
        form.setValue("isApproved", editingUser.isApproved);
        form.setValue("isAdmin", editingUser.isAdmin);
        form.setValue("groups", editingUser.groups || []);
        // @ts-ignore
        if (form.getValues().password) form.setValue("password", "");
        // @ts-ignore
        if (form.getValues().confirmPassword) form.setValue("confirmPassword", "");
      } else {
        // Reset to default "add" mode values
        form.reset({
          name: "",
          email: "",
          password: "",
          confirmPassword: "",
          allowEmails: true,
          displayEmailToGroupMembers: true,
          isApproved: true,
          isAdmin: false,
          groups: [],
        });
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFormDialogOpen, editingUser]);


  async function onSubmit(values: AddUserFormValues | EditUserFormValues) {
    let success = false;
    let toastMessage = "";

    if (editingUser) {
      // Edit User Logic
      const { password, confirmPassword, ...updateData } = values as AddUserFormValues;
      const validationResult = editUserFormSchema.safeParse(updateData);
      if (!validationResult.success) {
        Object.entries(validationResult.error.flatten().fieldErrors).forEach(([fieldName, messages]) => {
          if (messages && messages.length > 0) {
            // @ts-ignore
            form.setError(fieldName as keyof EditUserFormValues, { type: 'manual', message: messages[0] });
          }
        });
        toast({ title: "Validation Error", description: "Please correct the highlighted fields.", variant: "destructive" });
        return;
      }

      const userDataToUpdate: Partial<Omit<User, 'id' | 'avatarUrl' | 'password' | 'createdAt' | 'updatedAt'>> = validationResult.data;
      
      success = await updateUser(editingUser.id, userDataToUpdate);
      toastMessage = success ? `${validationResult.data.name} has been updated.` : "Could not update user. Email might already exist in Firestore.";
    } else {
      // Add New User Logic
      const validationResult = addUserFormSchema.safeParse(values);
       if (!validationResult.success) {
        Object.entries(validationResult.error.flatten().fieldErrors).forEach(([fieldName, messages]) => {
          if (messages && messages.length > 0) {
             // @ts-ignore
            form.setError(fieldName as keyof AddUserFormValues, { type: 'manual', message: messages[0] });
          }
        });
        toast({ title: "Validation Error", description: "Please correct the highlighted fields.", variant: "destructive" });
        return;
      }

      const addValues = validationResult.data as AddUserFormValues;

      if (!addValues.password || addValues.password !== addValues.confirmPassword) {
        if(!addValues.password) form.setError("password", { type: "manual", message: "Password is required for new users." });
        if(addValues.password !== addValues.confirmPassword) form.setError("confirmPassword", { type: "manual", message: "Passwords must match." });
        return;
      }
      
      try {
        const result = await addUser({
          name: addValues.name,
          email: addValues.email,
          password: addValues.password,
          allowEmails: addValues.allowEmails,
          displayEmailToGroupMembers: addValues.displayEmailToGroupMembers,
          isApproved: addValues.isApproved,
          isAdmin: addValues.isAdmin,
          groups: addValues.groups,
        });
        
        success = result.success;
        toastMessage = success ? `User ${addValues.name} created successfully.` : "Could not create user. Email might already exist or password issue.";
      } catch (error) {
        success = false;
        toastMessage = "Operation cancelled or failed. Please try again.";
      }
    }

    toast({
      title: editingUser ? (success ? "User Updated" : "Update Failed") : (success ? "User Created" : "Failed to Create User"),
      description: toastMessage,
      variant: success ? "default" : "destructive",
      duration: success ? 5000 : 7000,
    });

    if (success) {
      setIsFormDialogOpen(false);
      setEditingUser(null);
    }
  }

  const handleOpenAddDialog = () => {
    setEditingUser(null);
    form.reset({
      name: "", email: "", password: "", confirmPassword: "",
      allowEmails: true, displayEmailToGroupMembers: true,
      isApproved: true, isAdmin: false, groups: []
    });
    setIsFormDialogOpen(true);
  };

  const handleOpenEditDialog = (user: User) => {
    setEditingUser(user);
     form.reset({
        name: user.name, email: user.email,
        allowEmails: user.allowEmails, displayEmailToGroupMembers: user.displayEmailToGroupMembers,
        isApproved: user.isApproved, isAdmin: user.isAdmin,
        groups: user.groups || []
    });
    setIsFormDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (userToDelete && currentUser) {
      const result = await deleteUser(userToDelete.id);
      if (result.success) {
        toast({ title: "User Deleted", description: result.message || `User "${userToDelete.name}" and their Firestore data have been deleted.` });
        // await refreshAllDataFromFirestore(); // Context should update list
      } else {
        toast({ title: "Delete Failed", description: result.message || "Could not delete the user.", variant: "destructive" });
      }
      setUserToDelete(null);
    }
  };

  if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manage Users</h1>
          <p className="text-muted-foreground">View, add, edit, or remove user profiles from Firestore.</p>
        </div>
        <Dialog open={isFormDialogOpen} onOpenChange={(isOpen) => {
          setIsFormDialogOpen(isOpen);
          if (!isOpen) setEditingUser(null);
        }}>
          <DialogTrigger asChild>
            <Button onClick={handleOpenAddDialog}>
              <UserPlus className="mr-2 h-4 w-4" /> Add New User
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingUser ? "Edit User Profile" : "Add New User"}</DialogTitle>
              <DialogDescriptionComponentDialog>
                {editingUser
                  ? "Update the Firestore profile details for this user."
                  : <>
                      Create a new Firebase Authentication user and their GiftLink profile.
                      <strong className="block mt-2 text-destructive">Warning: Creating a new user will log you out of your current admin session. You will need to log back in.</strong>
                    </>
                }
              </DialogDescriptionComponentDialog>
            </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-1">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl><Input placeholder="e.g., John Doe" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                   <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                      <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl><Input type="email" placeholder="<EMAIL>" {...field} readOnly={!!editingUser} className={editingUser ? "bg-muted cursor-not-allowed" : ""} /></FormControl>
                          {editingUser && <FormDescriptionForm>Email cannot be changed after user creation for Auth consistency.</FormDescriptionForm>}
                          <FormMessage />
                      </FormItem>
                      )}
                  />
                  {!editingUser && (
                     <>
                        <FormField
                            control={form.control}
                            // @ts-ignore
                            name="password"
                            render={({ field }) => (
                                <FormItem>
                                <FormLabel>Password</FormLabel>
                                <FormControl><Input type="password" placeholder="Enter initial password" {...field} /></FormControl>
                                <FormDescriptionForm>Min. 6 characters. User will be prompted to change this if desired.</FormDescriptionForm>
                                <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            // @ts-ignore
                            name="confirmPassword"
                            render={({ field }) => (
                                <FormItem>
                                <FormLabel>Confirm Password</FormLabel>
                                <FormControl><Input type="password" placeholder="Confirm initial password" {...field} /></FormControl>
                                <FormMessage />
                                </FormItem>
                            )}
                        />
                     </>
                  )}

                  {editingUser && (
                     <Alert variant="default" className="bg-blue-50 border-blue-300 text-blue-700">
                        <Info className="h-5 w-5 !text-blue-600" />
                        <AlertTitle className="font-semibold">Password Management</AlertTitle>
                        <AlertDescription>
                        To change an existing user's password, use the Firebase Authentication console.
                        </AlertDescription>
                    </Alert>
                  )}
                  <FormField
                    control={form.control}
                    name="groups"
                    render={() => (
                      <FormItem>
                        <FormLabel>Assign to Groups/Families</FormLabel>
                        <FormDescriptionForm>User must belong to at least one group.</FormDescriptionForm>
                        <ScrollArea className="h-32 rounded-md border p-2">
                          {groupsData.map((group) => (
                            <FormField
                              key={group.id}
                              control={form.control}
                              name="groups"
                              render={({ field }) => (
                                <FormItem key={group.id} className="flex flex-row items-start space-x-3 space-y-0 my-1">
                                  <FormControl>
                                    <Checkbox
                                      checked={(field.value || []).includes(group.id)}
                                      onCheckedChange={(checked) => {
                                        return checked
                                          ? field.onChange([...(field.value || []), group.id])
                                          : field.onChange((field.value || []).filter((value) => value !== group.id));
                                      }}
                                    />
                                  </FormControl>
                                  <FormLabel className="font-normal">{group.name}</FormLabel>
                                </FormItem>
                              )}
                            />
                          ))}
                        </ScrollArea>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-2 gap-4 pt-2">
                    <FormField
                      control={form.control}
                      name="allowEmails"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3">
                          <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                          <div className="space-y-0.5">
                            <FormLabel className="font-normal leading-tight">Allow system email notifications</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="displayEmailToGroupMembers"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3">
                          <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                           <div className="space-y-0.5">
                            <FormLabel className="font-normal leading-tight">Display email to Groups</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="isApproved"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3">
                          <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                          <FormLabel className="font-normal leading-tight">Approved User</FormLabel>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="isAdmin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3">
                          <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                          <FormLabel className="font-normal leading-tight">Admin User</FormLabel>
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end gap-2 pt-2">
                    <DialogClose asChild>
                      <Button type="button" variant="outline">Cancel</Button>
                    </DialogClose>
                    <Button type="submit">{editingUser ? "Save Changes" : "Create User"}</Button>
                  </div>
                </form>
              </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Alert variant="default" className="bg-yellow-50 border-yellow-300 text-yellow-700">
        <ShieldAlert className="h-5 w-5 !text-yellow-600" />
        <AlertTitle className="font-semibold">User Management Notice</AlertTitle>
        <AlertDescription>
          Creating a user here will create both their Firebase Authentication account and their GiftLink profile. Email addresses cannot be changed after creation. Admins cannot delete themselves or the last remaining admin.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>User List</CardTitle>
          <CardDescription>A list of all user profiles in Firestore.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow><TableHead>Name</TableHead><TableHead>Email</TableHead><TableHead className="text-center">Approved</TableHead><TableHead className="text-center">Admin</TableHead><TableHead className="text-center">Allows Sys. Emails</TableHead><TableHead className="text-center">Email Visible</TableHead><TableHead className="text-center">Groups</TableHead><TableHead className="text-right">Actions</TableHead></TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell className="text-center">
                    {user.isApproved ?
                        <CheckCircle2 className="h-5 w-5 text-green-500 mx-auto" /> :
                        <XCircle className="h-5 w-5 text-red-500 mx-auto" />}
                  </TableCell>
                  <TableCell className="text-center">
                    {user.isAdmin ?
                        <ShieldCheck className="h-5 w-5 text-primary mx-auto" /> :
                        <span className="text-muted-foreground">-</span>}
                  </TableCell>
                  <TableCell className="text-center">
                    {user.allowEmails ?
                        <MailCheck className="h-5 w-5 text-green-500 mx-auto" /> :
                        <MailX className="h-5 w-5 text-red-500 mx-auto" />}
                  </TableCell>
                   <TableCell className="text-center">
                    {user.displayEmailToGroupMembers ?
                        <Eye className="h-5 w-5 text-green-500 mx-auto" /> :
                        <EyeOff className="h-5 w-5 text-red-500 mx-auto" />}
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="secondary">{(user.groups || []).length}</Badge>
                  </TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button variant="outline" size="icon" onClick={() => handleOpenEditDialog(user)}>
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit User</span>
                    </Button>
                    <AlertDialog open={!!userToDelete && userToDelete.id === user.id} onOpenChange={(isOpen) => { if (!isOpen) setUserToDelete(null); }}>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" size="icon" onClick={() => setUserToDelete(user)} disabled={currentUser?.id === user.id || (user.isAdmin && users.filter(u=>u.isAdmin).length <=1 )}>
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete User</span>
                        </Button>
                      </AlertDialogTrigger>
                      {userToDelete && userToDelete.id === user.id && (
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitleComponent>Are you sure?</AlertDialogTitleComponent>
                            <AlertDialogDescriptionDialogComponent>
                              This action cannot be undone. This will permanently delete the user profile "{userToDelete.name}" from Firestore and all their associated data (wishlist items, commitments). Corresponding Firebase Authentication user must be deleted separately from the Firebase Console.
                            </AlertDialogDescriptionDialogComponent>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setUserToDelete(null)}>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleConfirmDelete}>Delete User Profile</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      )}
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

    