"use client";

import Link from 'next/link';
import { useState, useMemo } from 'react';
import { useWishlist } from '@/contexts/WishlistContext';
import { WishlistItemCard } from '@/components/wishlists/WishlistItemCard';
import { AddWishlistItemForm } from '@/components/wishlists/AddWishlistItemForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ArrowRight, Eye, Info, Search, Users, Heart, ShieldAlert, Loader2, Gift, ArrowLeft, User, Trash2, Edit } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import type { User as AppUserType, WishlistItem } from '@/lib/types';

// Delete reason schema
const deleteReasonSchema = z.object({
  reason: z.string().min(5, "Reason must be at least 5 characters.").max(200),
});
type DeleteReasonFormValues = z.infer<typeof deleteReasonSchema>;

export default function AdminWishlistsPage() {
  const { currentUser, users: contextUsers, getItemsForUser, authLoading, deleteAnyWishlistItemByAdmin, updateAnyWishlistItem, refreshAllDataFromFirestore } = useWishlist();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [itemSearchTerm, setItemSearchTerm] = useState('');
  const [itemToDelete, setItemToDelete] = useState<WishlistItem | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<WishlistItem | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const deleteReasonForm = useForm<DeleteReasonFormValues>({
    resolver: zodResolver(deleteReasonSchema),
    defaultValues: { reason: "" },
  });

  // Get selected user
  const selectedUser = useMemo(() => {
    if (!selectedUserId) return null;
    return contextUsers.find(user => user.id === selectedUserId) || null;
  }, [contextUsers, selectedUserId]);

  // Get selected user's items
  const selectedUserItems = useMemo(() => {
    if (!selectedUser || authLoading) return [];
    return getItemsForUser(selectedUser.id);
  }, [selectedUser, getItemsForUser, authLoading]);

  // Filter selected user's items based on search term
  const filteredSelectedUserItems = useMemo(() => {
    if (!selectedUserItems.length) return [];
    
    if (itemSearchTerm.trim()) {
      const searchLower = itemSearchTerm.toLowerCase();
      return selectedUserItems.filter(item => 
        item.name.toLowerCase().includes(searchLower) ||
        item.description?.toLowerCase().includes(searchLower) ||
        item.category?.toLowerCase().includes(searchLower)
      );
    }
    
    return selectedUserItems;
  }, [selectedUserItems, itemSearchTerm]);

  // Filter users based on search term (for overview)
  const filteredUsers = useMemo(() => {
    if (!contextUsers.length) return [];
    
    let filtered = contextUsers;
    
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = contextUsers.filter(user => 
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower)
      );
    }
    
    // Sort by name
    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  }, [contextUsers, searchTerm]);

  if (!currentUser?.isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive">Access Denied</h1>
        <p className="text-muted-foreground mt-2">You do not have permission to view this page.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <Loader2 className="animate-spin h-10 w-10 text-primary mb-4" />
          <p className="text-lg text-muted-foreground">Loading wishlists...</p>
        </div>
      </div>
    );
  }

  const getInitials = (name: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length === 1) return names[0][0] ? names[0][0].toUpperCase() : '';
    return (names[0][0] ? names[0][0].toUpperCase() : '') + (names[names.length - 1][0] ? names[names.length - 1][0].toUpperCase() : '');
  };

  const getUserStatusBadge = (user: AppUserType) => {
    if (user.isAdmin) {
      return <Badge variant="destructive" className="text-xs">Admin</Badge>;
    }
    if (user.isApproved) {
      return <Badge variant="default" className="text-xs">Approved</Badge>;
    }
    return <Badge variant="secondary" className="text-xs">Pending</Badge>;
  };

  const openDeleteConfirmation = (item: WishlistItem) => {
    setItemToDelete(item);
    deleteReasonForm.reset({ reason: "" });
    setIsDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async (values: DeleteReasonFormValues) => {
    if (itemToDelete && currentUser?.isAdmin) {
      const success = await deleteAnyWishlistItemByAdmin(itemToDelete.id, values.reason);
      if (success) {
        toast({ title: "Item Deleted", description: `"${itemToDelete.name}" has been deleted by admin.` });
        await refreshAllDataFromFirestore();
      } else {
        toast({ title: "Delete Failed", description: "Could not delete the item.", variant: "destructive" });
      }
      setIsDeleteConfirmOpen(false);
      setItemToDelete(null);
    }
  };

  const handleOpenEditDialog = (item: WishlistItem) => {
    setEditingItem(item);
    setIsEditDialogOpen(true);
  };

  const handleEditFormSubmit = () => {
    setIsEditDialogOpen(false);
    setEditingItem(null);
  };

  const handleAdminUpdateItem = async (updatedItemData: Partial<Omit<WishlistItem, 'id' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => {
    if (!editingItem || !currentUser?.isAdmin) {
      toast({ title: "Update Failed", description: "Could not update item. Admin permissions required or item details missing.", variant: "destructive" });
      return false;
    }
    
    const success = await updateAnyWishlistItem(editingItem.id, updatedItemData);
    if (success) {
      toast({ title: "Item Updated", description: `"${updatedItemData.name || editingItem.name}" has been updated by admin.` });
      handleEditFormSubmit();
      return true;
    } else {
      toast({ title: "Update Failed", description: "Could not update the item.", variant: "destructive" });
      return false;
    }
  };

  if (selectedUser) {
    // Show selected user's wishlist
    return (
      <div className="space-y-8">
        <section>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div>
              <Button 
                variant="ghost" 
                className="mb-4 -ml-4"
                onClick={() => {
                  setSelectedUserId(null);
                  setItemSearchTerm('');
                }}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Manage Wishlists
              </Button>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                <Heart className="h-8 w-8 text-primary" />
                {selectedUser.name}'s Wishlist
              </h1>
              <p className="text-muted-foreground mt-2">Admin view of {selectedUser.name}'s wishlist items.</p>
            </div>
          </div>

          {/* User Info Card */}
          <Card className="mb-6">
            <CardHeader>
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={selectedUser.avatarUrl} alt={selectedUser.name} data-ai-hint="person avatar" />
                  <AvatarFallback className="text-lg">{getInitials(selectedUser.name)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-xl">{selectedUser.name}</CardTitle>
                    {getUserStatusBadge(selectedUser)}
                  </div>
                  <CardDescription className="text-sm">{selectedUser.email}</CardDescription>
                  <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Gift className="h-4 w-4" />
                      <span>{selectedUserItems.length} items on wishlist</span>
                    </div>
                    {selectedUser.groups && selectedUser.groups.length > 0 && (
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>{selectedUser.groups.length} group{selectedUser.groups.length !== 1 ? 's' : ''}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Search Bar for Items */}
          {selectedUserItems.length > 0 && (
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search wishlist items..."
                value={itemSearchTerm}
                onChange={(e) => setItemSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          )}
        </section>

        <section className="space-y-6">
          {filteredSelectedUserItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredSelectedUserItems.map(item => (
                <div key={item.id} className="relative">
                  <WishlistItemCard
                    item={item}
                    itemOwner={selectedUser}
                    isOwnList={false}
                    onEdit={() => {}} // Admin view - no editing through card
                  />
                  {/* Admin Edit Button */}
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="absolute top-2 left-2 h-8 w-8 p-0 z-20 bg-white shadow-md hover:bg-blue-50 border-blue-200"
                    onClick={() => handleOpenEditDialog(item)}
                  >
                    <Edit className="h-4 w-4 text-blue-600" />
                    <span className="sr-only">Edit Item</span>
                  </Button>
                  {/* Admin Delete Button */}
                  <AlertDialog open={isDeleteConfirmOpen && itemToDelete?.id === item.id} onOpenChange={(isOpen) => {
                    if (!isOpen) setItemToDelete(null);
                    setIsDeleteConfirmOpen(isOpen);
                  }}>
                    <AlertDialogTrigger asChild>
                      <Button 
                        variant="destructive" 
                        size="sm" 
                        className="absolute top-2 right-2 h-8 w-8 p-0"
                        onClick={() => openDeleteConfirmation(item)}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete Item</span>
                      </Button>
                    </AlertDialogTrigger>
                    {itemToDelete && itemToDelete.id === item.id && (
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Item: {itemToDelete?.name}</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete this item from {selectedUser.name}'s wishlist? This action cannot be undone. Please provide a reason for deletion.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <Form {...deleteReasonForm}>
                          <form onSubmit={deleteReasonForm.handleSubmit(handleConfirmDelete)} className="space-y-4">
                            <FormField
                              control={deleteReasonForm.control}
                              name="reason"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Reason for Deletion</FormLabel>
                                  <FormControl>
                                    <Textarea placeholder="e.g., Item no longer available, Inappropriate item, Duplicate entry." {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <AlertDialogFooter>
                              <AlertDialogCancel onClick={() => {setIsDeleteConfirmOpen(false); setItemToDelete(null);}}>Cancel</AlertDialogCancel>
                              <Button type="submit" variant="destructive">Confirm Delete</Button>
                            </AlertDialogFooter>
                          </form>
                        </Form>
                      </AlertDialogContent>
                    )}
                  </AlertDialog>
                </div>
              ))}
            </div>
          ) : selectedUserItems.length === 0 ? (
            <Alert className="border-primary/50 text-primary bg-primary/5">
              <Info className="h-5 w-5 text-primary" />
              <AlertTitle className="font-semibold">No Items on Wishlist</AlertTitle>
              <AlertDescription>
                {selectedUser.name} hasn't added any items to their wishlist yet.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="border-primary/50 text-primary bg-primary/5">
              <Info className="h-5 w-5 text-primary" />
              <AlertTitle className="font-semibold">No Items Found</AlertTitle>
              <AlertDescription>
                No items match your search criteria "{itemSearchTerm}".
              </AlertDescription>
            </Alert>
          )}
        </section>

        {/* Edit Item Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Item (Admin)</DialogTitle>
              <DialogDescription>
                Update the details of this wishlist item. Changes will be visible to the item owner.
              </DialogDescription>
            </DialogHeader>
            <AddWishlistItemForm 
              itemToEdit={editingItem} 
              onFormSubmit={handleEditFormSubmit}
              isAdminEdit={true}
              onAdminSubmit={handleAdminUpdateItem}
            />
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  // Show overview with user selection
  return (
    <div className="space-y-8">
      <section>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Heart className="h-8 w-8 text-primary" />
              Manage Wishlists
            </h1>
            <p className="text-muted-foreground mt-2">View and manage all user wishlists in the system.</p>
          </div>
          <Button asChild variant="outline">
            <Link href="/admin">
              Back to Admin Dashboard
            </Link>
          </Button>
        </div>

        {/* User Selection */}
        <div className="mb-6">
          <label className="text-sm font-medium mb-2 block">Select a user to view their wishlist:</label>
          <Select value={selectedUserId || ""} onValueChange={(value) => setSelectedUserId(value || null)}>
            <SelectTrigger className="w-full max-w-md">
              <SelectValue placeholder="Choose a user..." />
            </SelectTrigger>
            <SelectContent>
              {contextUsers
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(user => (
                  <SelectItem key={user.id} value={user.id}>
                    <div className="flex items-center gap-2">
                      <span>{user.name}</span>
                      <span className="text-xs text-muted-foreground">({getItemsForUser(user.id).length} items)</span>
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        {/* Search Bar */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{contextUsers.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Users with Items</CardTitle>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4 text-muted-foreground" />
                <Gift className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contextUsers.filter(user => getItemsForUser(user.id).length > 0).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
              <Gift className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contextUsers.reduce((total, user) => total + getItemsForUser(user.id).length, 0)}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <section className="space-y-6">
        {filteredUsers.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredUsers.map(user => {
              const userItems = getItemsForUser(user.id);
              return (
                <Card key={user.id} className="shadow-md hover:shadow-lg transition-shadow">
                  <CardHeader className="flex flex-row items-center gap-3 space-y-0 pb-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={user.avatarUrl} alt={user.name} data-ai-hint="person avatar" />
                      <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <CardTitle className="text-lg truncate">{user.name}</CardTitle>
                        {getUserStatusBadge(user)}
                      </div>
                      <CardDescription className="text-xs truncate">
                        {user.email}
                      </CardDescription>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Items on wishlist:</span>
                        <span className="font-medium">{userItems.length}</span>
                      </div>
                      {userItems.length > 0 ? (
                        <ul className="text-xs space-y-1 text-muted-foreground">
                          {userItems.slice(0, 2).map(item => (
                            <li key={item.id} className="truncate"> • {item.name}</li>
                          ))}
                          {userItems.length > 2 && <li>... and {userItems.length - 2} more</li>}
                        </ul>
                      ) : (
                        <p className="text-xs text-muted-foreground">No items on their wishlist yet.</p>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="w-full"
                      onClick={() => setSelectedUserId(user.id)}
                    >
                      <Eye className="mr-2 h-4 w-4" /> View {user.name.split(' ')[0]}'s Wishlist
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        ) : (
          <Alert className="border-primary/50 text-primary bg-primary/5">
            <Info className="h-5 w-5 text-primary" />
            <AlertTitle className="font-semibold">
              {searchTerm ? 'No Users Found' : 'No Users Available'}
            </AlertTitle>
            <AlertDescription>
              {searchTerm 
                ? `No users match your search criteria "${searchTerm}".`
                : 'There are no users in the system yet.'
              }
            </AlertDescription>
          </Alert>
        )}
      </section>
    </div>
  );
}