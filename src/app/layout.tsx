
import type { Metada<PERSON> } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { WishlistProvider } from '@/contexts/WishlistContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
// Navbar is no longer imported here directly, AuthGuard will handle it.
import { Toaster } from "@/components/ui/toaster";
import { AuthGuard } from '@/components/AuthGuard';
import { PasswordDialogProvider } from '@/components/PasswordDialogProvider';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'GiftLink',
  description: 'Your personal gift registry for family and friends.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased flex flex-col min-h-screen`}
        suppressHydrationWarning={true}
      >
        <ThemeProvider>
          <WishlistProvider>
            <AuthGuard> {/* AuthGuard now wraps children directly and will manage Navbar/main layout */}
              {children}
            </AuthGuard>
            <Toaster />
            <PasswordDialogProvider />
          </WishlistProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
