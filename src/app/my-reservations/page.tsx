
"use client";

import { useState, useEffect, useMemo, useCallback } from 'react';
import {
  useWishlist,
  // getTotalCommittedQuantity, // No longer used directly here
  // getUserCommittedQuantity, // No longer used directly here
  // getReservableQuantity, // No longer used directly here
  // getDirectlyPurchasableQuantity // No longer used directly here
} from '@/contexts/WishlistContext';
import { getAvailableQuantity } from '@/lib/wishlistUtils'; // Import directly
import type { User, WishlistItem, Commitment, Category, Rank } from '@/lib/types';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ShoppingBag, BookmarkMinus, ExternalLink, Info, ImageIcon, RotateCcw, BookmarkCheck, Printer, EyeOff, Package, Tag, Star, Users, CalendarDays, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from '@/hooks/use-toast';
import { Badge } from "@/components/ui/badge";
import { QuantityCommitDialog } from '@/components/wishlists/QuantityCommitDialog';

interface UserCommitmentDisplay {
  owner: User;
  items: Array<{
    item: WishlistItem;
    commitmentsByCurrentUser: Commitment[]; // Only commitments by the current user for this item
  }>;
}

export default function MyCommitmentsPage() {
  const {
    currentUser,
    users,
    allItems, // Changed from wishlists
    unreserveItem,
    purchaseItem,
    returnItem,
    categories,
    ranks,
    authLoading,
   } = useWishlist();
  const { toast } = useToast();
  const [committedItemsByOwner, setCommittedItemsByOwner] = useState<UserCommitmentDisplay[]>([]);
  const [isPrintPreview, setIsPrintPreview] = useState(false);

  const [isCommitDialogOpen, setIsCommitDialogOpen] = useState(false);
  const [commitDialogAction, setCommitDialogAction] = useState<'purchase' | 'unreserve' | 'return' | null>(null);
  const [selectedItemForDialog, setSelectedItemForDialog] = useState<WishlistItem | null>(null);
  const [selectedOwnerForDialog, setSelectedOwnerForDialog] = useState<User | null>(null);
  const [selectedCommitmentForDialog, setSelectedCommitmentForDialog] = useState<Commitment | null>(null);


  useEffect(() => {
    if (!currentUser || !users.length || !allItems.length || authLoading) {
      setCommittedItemsByOwner([]);
      return;
    }

    const grouped: Record<string, UserCommitmentDisplay> = {};

    allItems.forEach(item => {
      const owner = users.find(u => u.id === item.ownerUid);
      if (!owner || owner.id === currentUser.id) return; // Skip item if owner not found or is current user

      const currentUserItemCommitments = (Array.isArray(item.commitments) ? item.commitments : []).filter(
        c => c.committedByUid === currentUser.id
      );

      if (currentUserItemCommitments.length > 0) {
        if (!grouped[owner.id]) {
          grouped[owner.id] = { owner, items: [] };
        }
        // Check if item already exists for this owner to avoid duplicates if an item had multiple commitments by user (e.g. different IDs but for same item)
        const existingItemEntry = grouped[owner.id].items.find(i => i.item.id === item.id);
        if (existingItemEntry) {
           // This case should ideally not happen if commitments are correctly managed (one reservation, one purchase per user per item)
           // But if it does, merge them or log a warning. For now, we assume it's one entry per item.
           console.warn("Duplicate item entry found in MyCommitments, merging commitments for item ID:", item.id);
           existingItemEntry.commitmentsByCurrentUser.push(...currentUserItemCommitments);
           // Deduplicate if necessary, though this scenario should be rare with proper commitment logic
           existingItemEntry.commitmentsByCurrentUser = Array.from(new Set(existingItemEntry.commitmentsByCurrentUser.map(c => c.id))).map(id => existingItemEntry.commitmentsByCurrentUser.find(c => c.id === id)!);

        } else {
           grouped[owner.id].items.push({ item, commitmentsByCurrentUser: currentUserItemCommitments });
        }
      }
    });
    setCommittedItemsByOwner(Object.values(grouped).filter(group => group.items.length > 0));
  }, [currentUser, users, allItems, authLoading]);

  const getInitials = (name: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length === 1) return names[0][0] ? names[0][0].toUpperCase() : '';
    return (names[0][0] ? names[0][0].toUpperCase() : '') + (names[names.length - 1][0] ? names[names.length - 1][0].toUpperCase() : '');
  }

  const handleOpenCommitDialog = async (
    action: 'purchase' | 'unreserve' | 'return',
    item: WishlistItem,
    owner: User,
    commitment?: Commitment // Pass the specific commitment being acted upon
  ) => {
    if (!currentUser) return;
    const itemName = item.name;
    const ownerId = owner.id; // item.ownerUid is the same
    const itemId = item.id;
    let success = false;

    // Handle single quantity actions directly if commitment is for 1 unit
    if (commitment && commitment.quantity === 1) {
      if (action === 'unreserve') {
        success = await unreserveItem(ownerId, itemId, 1, commitment.id);
        if (success) toast({ title: `Item Unreserved`, description: `1 unit of "${itemName}" unreserved.` });
        else toast({ title: "Action Failed", description: `Could not unreserve "${itemName}".`, variant: "destructive" });
        return;
      }
      if (action === 'return') {
        success = await returnItem(ownerId, itemId, 1, commitment.id);
        if (success) toast({ title: `Item Returned`, description: `1 unit of "${itemName}" returned.` });
        else toast({ title: "Action Failed", description: `Could not return "${itemName}".`, variant: "destructive" });
        return;
      }
      if (action === 'purchase' && commitment.type === 'reserved') {
        const itemAvailableForPurchase = getAvailableQuantity(item);
        if (itemAvailableForPurchase >= 1) {
            success = await purchaseItem(ownerId, itemId, 1, commitment.id); // Pass commitment.id as sourceCommitmentId
            if (success) toast({ title: `Item Purchased`, description: `1 unit of "${itemName}" marked as purchased.` });
            else toast({ title: "Action Failed", description: `Could not purchase "${itemName}".`, variant: "destructive" });
        } else {
            toast({ title: "Purchase Failed", description: `No units of "${itemName}" are available to purchase.`, variant: "destructive" });
        }
        return;
      }
    }

    // For quantities > 1 or if no specific commitment implies creating a new 'purchase' from scratch
    setSelectedItemForDialog(item);
    setSelectedOwnerForDialog(owner);
    setCommitDialogAction(action);
    setSelectedCommitmentForDialog(commitment || null);
    setIsCommitDialogOpen(true);
  };

  const handleSubmitCommitDialog = async (quantity: number, commitmentIdToModify?: string) => {
    if (!currentUser || !commitDialogAction || !selectedItemForDialog || !selectedOwnerForDialog) return;

    let success = false;
    let actionText = "";
    const itemName = selectedItemForDialog.name;
    const ownerId = selectedOwnerForDialog.id; // Or selectedItemForDialog.ownerUid
    const itemId = selectedItemForDialog.id;

    if (commitDialogAction === 'purchase') {
      // If selectedCommitmentForDialog exists and is a reservation, it's a conversion.
      // Otherwise, it's a new direct purchase.
      const sourceCommitmentId = selectedCommitmentForDialog?.type === 'reserved' ? selectedCommitmentForDialog.id : undefined;
      success = await purchaseItem(ownerId, itemId, quantity, sourceCommitmentId);
      actionText = "purchased";
    } else if (commitDialogAction === 'unreserve') {
      success = await unreserveItem(ownerId, itemId, quantity, commitmentIdToModify); // commitmentIdToModify is passed if acting on existing
      actionText = "unreserved";
    } else if (commitDialogAction === 'return') {
      success = await returnItem(ownerId, itemId, quantity, commitmentIdToModify); // commitmentIdToModify is passed if acting on existing
      actionText = "returned";
    }

    if (success) {
      toast({
        title: `Item ${actionText}`,
        description: `${quantity} unit(s) of "${itemName}" ${actionText}.`,
      });
    } else {
      toast({
        title: "Action Failed",
        description: `Could not ${commitDialogAction} ${quantity} unit(s) of "${itemName}".`,
        variant: "destructive",
      });
    }
    setIsCommitDialogOpen(false);
    setSelectedItemForDialog(null);
    setSelectedOwnerForDialog(null);
    setCommitDialogAction(null);
    setSelectedCommitmentForDialog(null);
  };


  const handlePrintClick = useCallback(() => {
    setIsPrintPreview(true);
  }, []);

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="flex flex-col items-center">
          <Loader2 className="animate-spin h-8 w-8 text-primary mb-4" />
          <p className="text-muted-foreground">Loading your commitments...</p>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return <p>Loading user data or please log in...</p>;
  }

  return (
    <>
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <ShoppingBag className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold tracking-tight">My Gift Commitments</h1>
          </div>
          {!isPrintPreview ? (
            <Button
              variant="outline"
              onClick={handlePrintClick}
              className="non-printable w-full sm:w-auto"
            >
              <Printer className="mr-2 h-4 w-4" />
              Print View
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={() => setIsPrintPreview(false)}
              className="non-printable w-full sm:w-auto"
            >
              <EyeOff className="mr-2 h-4 w-4" />
              Exit Print Preview
            </Button>
          )}
        </div>

        {isPrintPreview ? (
          <div className="pt-4">
            {committedItemsByOwner.length > 0 ? (
              committedItemsByOwner.map(({ owner, items: committedUserDisplayItems }) => (
                <div key={`print-${owner.id}`} className="mb-6 page-break-after">
                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="h-10 w-10">
                        <AvatarImage src={owner.avatarUrl} alt={owner.name} data-ai-hint="person avatar" />
                        <AvatarFallback>{getInitials(owner.name)}</AvatarFallback>
                      </Avatar>
                    <h2 className="text-xl font-semibold">
                      Gifts for: {owner.name}
                    </h2>
                  </div>
                  <div className="space-y-3 pl-4 border-l-2 border-border ml-5">
                    {committedUserDisplayItems.map(({ item, commitmentsByCurrentUser }) =>
                      commitmentsByCurrentUser.map(commitment => ( // Iterate through specific user's commitments for this item
                        <div key={`print-commitment-${commitment.id}`} className="pb-3 border-b border-gray-200 last:border-b-0">
                          <div className="flex gap-4 items-start">
                            {item.imageUrl && (
                              <div className="w-20 h-20 flex-shrink-0 bg-gray-100 rounded overflow-hidden border border-gray-200">
                                <Image
                                  src={item.imageUrl}
                                  alt={item.name}
                                  width={80}
                                  height={80}
                                  className="object-cover w-full h-full" data-ai-hint="product small"
                                />
                              </div>
                            )}
                            <div className="flex-1">
                              <h3 className="text-base font-medium text-gray-800">{item.name} (Qty: {commitment.quantity})</h3>
                              <p className="text-sm text-gray-700">${item.price.toFixed(2)} {item.retailer && ` - ${item.retailer}`}</p>
                              <p className="text-sm font-semibold">
                                Status: <span className={commitment.type === 'purchased' ? "text-accent" : "text-primary"}>{commitment.type === 'purchased' ? 'Purchased' : 'Reserved'}</span>
                              </p>
                              {item.categoryId && <p className="text-xs text-gray-600 mt-0.5">Category: {categories.find(c=>c.id === item.categoryId)?.title || 'N/A'}</p>}
                              {item.rankId && <p className="text-xs text-gray-600 mt-0.5">Ranking: {ranks.find(r=>r.id === item.rankId)?.title || 'N/A'}</p>}
                              {item.description && (
                                <p className="text-xs text-gray-600 mt-1">{item.description}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-600">You have no gift commitments to display in print preview.</p>
            )}
             {isPrintPreview && (
              <Button variant="outline" onClick={() => window.print()} className="non-printable mt-6">
                <Printer className="mr-2 h-4 w-4" /> Print Now
              </Button>
            )}
          </div>
        ) : (
          <div className="print:hidden">
            {committedItemsByOwner.length > 0 ? (
              <Accordion type="multiple" className="space-y-4">
                {committedItemsByOwner.map(({ owner, items: committedUserDisplayItems }) => (
                  <AccordionItem key={owner.id} value={owner.id} className="border border-border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-card">
                    <AccordionTrigger className="p-4 hover:no-underline">
                      <div className="flex items-center gap-4 w-full">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={owner.avatarUrl} alt={owner.name} data-ai-hint="person avatar" />
                          <AvatarFallback>{getInitials(owner.name)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 text-left">
                          <p className="font-medium">Items for: {owner.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {committedUserDisplayItems.reduce((sum, cui) => sum + cui.commitmentsByCurrentUser.reduce((cSum, c) => cSum + c.quantity, 0), 0)} item unit(s) committed across {committedUserDisplayItems.length} distinct item(s)
                          </p>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="p-4 border-t border-border space-y-3">
                        {committedUserDisplayItems.map(({ item, commitmentsByCurrentUser }) =>
                          commitmentsByCurrentUser.map(commitment => ( // Iterate through specific user's commitments for this item
                            <div key={commitment.id} className="flex flex-col sm:flex-row gap-4 p-3 border rounded-md bg-background/50">
                              <div className="relative h-20 w-20 sm:h-24 sm:w-24 flex-shrink-0 rounded border bg-muted flex items-center justify-center overflow-hidden">
                                {item.imageUrl ? (
                                  <Image
                                    src={item.imageUrl}
                                    alt={item.name}
                                    fill
                                    className="object-cover"
                                    data-ai-hint="product small"
                                  />
                                ) : (
                                  <ImageIcon className="h-8 w-8 text-muted-foreground" />
                                )}
                              </div>
                              <div className="flex-1 space-y-1">
                                <h4 className="font-semibold">{item.name}</h4>
                                <p className="text-xs text-muted-foreground">For: {owner.name}</p>
                                <div className="flex justify-between items-center">
                                  <p className="text-lg text-primary font-medium">${item.price.toFixed(2)}</p>
                                  <Badge variant="outline" className="text-xs">Your Qty: {commitment.quantity}</Badge>
                                </div>
                                <p className="text-xs text-muted-foreground">From: {item.retailer}</p>
                                {item.categoryId && <p className="text-xs text-muted-foreground flex items-center"><Tag className="h-3 w-3 mr-1 text-muted-foreground/70"/> {categories.find(c=>c.id === item.categoryId)?.title || 'N/A'}</p>}
                                {item.rankId && <p className="text-xs text-muted-foreground flex items-center"><Star className="h-3 w-3 mr-1 text-muted-foreground/70"/> {ranks.find(r=>r.id === item.rankId)?.title || 'N/A'}</p>}
                                {commitment.type === 'purchased' && <Badge className="bg-accent text-accent-foreground"><ShoppingBag className="mr-1 h-3 w-3" />Purchased</Badge>}
                                {commitment.type === 'reserved' && <Badge className="bg-primary text-primary-foreground"><BookmarkCheck className="mr-1 h-3 w-3" />Reserved</Badge>}
                                {item.description && <p className="text-xs text-foreground/80 line-clamp-2 pt-1">{item.description}</p>}
                              </div>
                              <div className="flex flex-col sm:items-end justify-center gap-2 mt-2 sm:mt-0">
                                {item.url && (
                                  <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                                    <Link href={item.url} target="_blank" rel="noopener noreferrer">
                                      <ExternalLink /> View Online
                                    </Link>
                                  </Button>
                                )}
                                {commitment.type === 'reserved' && (
                                  <>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleOpenCommitDialog('unreserve', item, owner, commitment)}
                                      className="w-full sm:w-auto"
                                    >
                                      <BookmarkMinus /> Unreserve
                                    </Button>
                                    <Button
                                      variant="default"
                                      size="sm"
                                      onClick={() => handleOpenCommitDialog('purchase', item, owner, commitment)}
                                      className="w-full sm:w-auto"
                                      disabled={getAvailableQuantity(item) < commitment.quantity && commitment.quantity > 0}
                                    >
                                      <ShoppingBag /> Purchased
                                    </Button>
                                  </>
                                )}
                                {commitment.type === 'purchased' && (
                                  <Button
                                    variant="secondary"
                                    size="sm"
                                    onClick={() => handleOpenCommitDialog('return', item, owner, commitment)}
                                    className="w-full sm:w-auto"
                                  >
                                    <RotateCcw /> Mark Returned
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            ) : (
              <Alert className="border-primary/50 text-primary bg-primary/5">
                <Info className="h-5 w-5 text-primary" />
                <AlertTitle className="font-semibold">No Commitments Yet</AlertTitle>
                <AlertDescription>
                  You haven't reserved or purchased any items from your friends' wishlists yet.
                  Browse their lists to find the perfect gift!
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>
      {selectedItemForDialog && selectedOwnerForDialog && commitDialogAction && currentUser && (
        <QuantityCommitDialog
          isOpen={isCommitDialogOpen}
          onOpenChange={setIsCommitDialogOpen}
          item={selectedItemForDialog}
          actionType={commitDialogAction}
          commitmentId={selectedCommitmentForDialog?.id} // Pass the specific commitment ID
          maxQuantity={
             (() => {
                if (!selectedItemForDialog || !currentUser) return 0;
                switch(commitDialogAction) {
                    case 'unreserve': return selectedCommitmentForDialog?.quantity ?? 0;
                    case 'return': return selectedCommitmentForDialog?.quantity ?? 0;
                    case 'purchase':
                        // If converting a reservation, max is the lesser of reserved qty or item available
                        if (selectedCommitmentForDialog && selectedCommitmentForDialog.type === 'reserved') {
                            return Math.min(selectedCommitmentForDialog.quantity, getAvailableQuantity(selectedItemForDialog));
                        }
                        // If direct purchase, max is item available
                        return getAvailableQuantity(selectedItemForDialog);
                    default: return 0; // Should not happen for these actions
                }
            })()
          }
          currentCommittedQuantity={ // This is the quantity of the *specific commitment* being modified
             (commitDialogAction === 'unreserve' || commitDialogAction === 'return' || (commitDialogAction === 'purchase' && selectedCommitmentForDialog?.type === 'reserved'))
             ? selectedCommitmentForDialog?.quantity
             : undefined // Not relevant for new direct purchases in this dialog context
          }
          onSubmit={handleSubmitCommitDialog}
        />
      )}
    </>
  );
}

