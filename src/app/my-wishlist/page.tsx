
"use client";

import { useState, useMemo, useEffect } from 'react';
import { useWishlist } from '@/contexts/WishlistContext';
import { WishlistItemCard } from '@/components/wishlists/WishlistItemCard';
import { AddWishlistItemForm } from '@/components/wishlists/AddWishlistItemForm';
import { Button } from '@/components/ui/button';
import { PlusCircle, ListChecks, Info, LayoutGrid, ListFilter, ArrowUp, ArrowDown, Filter, CalendarDays, Loader2 } from 'lucide-react';
import type { WishlistItem as WishlistItemType } from '@/lib/types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { WishlistItemRow } from '@/components/wishlists/WishlistItemRow';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { cn } from '@/lib/utils';

type ViewMode = 'grid' | 'list';
type SortableWishlistItemKey = keyof Pick<WishlistItemType, 'name' | 'price' | 'dateAdded' | 'categoryId' | 'rankId'> | 'default';


export default function MyWishlistPage() {
  const { currentUser, getItemsForUser, categories, ranks, authLoading } = useWishlist(); // Added getItemsForUser
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<WishlistItemType | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  const [sortConfig, setSortConfig] = useState<{ key: SortableWishlistItemKey; direction: 'asc' | 'desc' } | null>({ key: 'dateAdded', direction: 'desc'});
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>([]);

  // No longer need localCurrentUserWishlist or isLoadingWishlist, items come directly from context.
  // The authLoading state from context can be used for the initial loading phase.

  const currentUserItems = useMemo(() => {
    if (!currentUser) return [];
    return getItemsForUser(currentUser.id);
  }, [currentUser, getItemsForUser]);


  const processedItems = useMemo(() => {
    let items = currentUserItems;

    if (selectedCategoryIds.length > 0) {
      items = items.filter(item => item.categoryId && selectedCategoryIds.includes(item.categoryId));
    }

    if (sortConfig && sortConfig.key !== 'default') {
      items = [...items].sort((a, b) => {
        let keyToSortBy = sortConfig.key;
        let valA, valB;

        if (keyToSortBy === 'categoryId') {
          valA = categories.find(c => c.id === a.categoryId)?.title?.toLowerCase();
          valB = categories.find(c => c.id === b.categoryId)?.title?.toLowerCase();
        } else if (keyToSortBy === 'rankId') {
          valA = ranks.find(r => r.id === a.rankId)?.title?.toLowerCase();
          valB = ranks.find(r => r.id === b.rankId)?.title?.toLowerCase();
        } else {
          valA = a[keyToSortBy as keyof WishlistItemType];
          valB = b[keyToSortBy as keyof WishlistItemType];
        }
        
        let comparison = 0;

        if (valA == null && valB == null) comparison = 0;
        else if (valA == null) comparison = 1;
        else if (valB == null) comparison = -1;
        else if (typeof valA === 'number' && typeof valB === 'number') {
          comparison = valA - valB;
        } else if (keyToSortBy === 'dateAdded' && typeof valA === 'string' && typeof valB === 'string') {
          comparison = new Date(valA).getTime() - new Date(valB).getTime();
        } else if (typeof valA === 'string' && typeof valB === 'string') {
          comparison = valA.toLowerCase().localeCompare(valB.toLowerCase());
        }
        return sortConfig.direction === 'asc' ? comparison : comparison * -1;
      });
    }
    return items;
  }, [currentUserItems, sortConfig, selectedCategoryIds, categories, ranks]);


  const handleEditItem = (item: WishlistItemType) => {
    setEditingItem(item);
    setIsFormOpen(true);
  };

  const openAddItemForm = () => {
    setEditingItem(null);
    setIsFormOpen(true);
  };

  const handleFormSubmit = () => {
    setIsFormOpen(false);
    setEditingItem(null);
    // Data re-fetch is handled by WishlistContext after add/update operations
  };

  const handleSortChange = (key: SortableWishlistItemKey) => {
    if (!sortConfig || sortConfig.key !== key) {
      setSortConfig({ key, direction: key === 'price' ? 'asc' : 'desc' }); 
    } else {
       if (key === 'default') {
        setSortConfig({ key: 'dateAdded', direction: 'desc'});
      } else {
        setSortConfig({
          key,
          direction: sortConfig.direction === 'asc' ? 'desc' : 'asc',
        });
      }
    }
  };

  const toggleSortDirection = () => {
    if (sortConfig && sortConfig.key !== 'default') {
      setSortConfig({
        ...sortConfig,
        direction: sortConfig.direction === 'asc' ? 'desc' : 'asc',
      });
    }
  };

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="flex flex-col items-center">
          <Loader2 className="animate-spin h-8 w-8 text-primary mb-4" />
          <p className="text-muted-foreground">Loading your wishlist...</p>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return <p>Please log in to view your wishlist.</p>;
  }


  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <ListChecks className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold tracking-tight">My Wishlist</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setViewMode('grid')}
            aria-label="Grid view"
          >
            <LayoutGrid className="h-5 w-5" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setViewMode('list')}
            aria-label="List view"
          >
            <ListFilter className="h-5 w-5" />
          </Button>
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button onClick={openAddItemForm} className="w-full sm:w-auto">
                <PlusCircle className="mr-2 h-4 w-4" /> Add New Item
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{editingItem ? 'Edit Item' : 'Add New Item'}</DialogTitle>
                <DialogDescription>
                  {editingItem ? 'Update the details of your wishlist item.' : 'Fill in the details of the item you wish to add.'}
                </DialogDescription>
              </DialogHeader>
              <AddWishlistItemForm itemToEdit={editingItem} onFormSubmit={handleFormSubmit} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Sorting and Filtering Controls */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4 items-center p-4 border rounded-lg bg-card shadow">
        <div className="flex items-center gap-2">
          <Label htmlFor="sort-key" className="text-sm font-medium">Sort by:</Label>
          <Select
            value={sortConfig?.key || 'default'}
            onValueChange={(value) => handleSortChange(value as SortableWishlistItemKey)}
          >
            <SelectTrigger id="sort-key" className="w-auto sm:w-[160px] text-sm">
              <SelectValue placeholder="Select field" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">Default (Date Added)</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="price">Price</SelectItem>
              <SelectItem value="dateAdded">Date Added</SelectItem>
              <SelectItem value="categoryId">Category</SelectItem>
              <SelectItem value="rankId">Ranking</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {sortConfig && sortConfig.key !== 'default' && (
          <Button variant="outline" size="icon" onClick={toggleSortDirection} aria-label="Toggle sort direction">
            {sortConfig.direction === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
          </Button>
        )}

        <div className="flex items-center gap-2 sm:ml-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto text-sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter Categories ({selectedCategoryIds.length > 0 ? selectedCategoryIds.length : 'All'})
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-60 max-h-80 overflow-y-auto">
              <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {categories.length > 0 ? categories.map((category) => (
                <DropdownMenuCheckboxItem
                  key={category.id}
                  checked={selectedCategoryIds.includes(category.id)}
                  onCheckedChange={(checked) => {
                    setSelectedCategoryIds((prev) =>
                      checked
                        ? [...prev, category.id]
                        : prev.filter((id) => id !== category.id)
                    );
                  }}
                >
                  {category.title}
                </DropdownMenuCheckboxItem>
              )) : (
                <DropdownMenuItem disabled>No categories available</DropdownMenuItem>
              )}
              {selectedCategoryIds.length > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onSelect={() => setSelectedCategoryIds([])}
                    className="text-destructive focus:text-destructive hover:bg-destructive/10"
                  >
                    Clear Filters
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>


      {processedItems.length > 0 ? (
        viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {processedItems.map((item) => (
              <WishlistItemCard
                key={item.id}
                item={item}
                itemOwner={currentUser} // On this page, currentUser is always the itemOwner
                isOwnList={true}
                onEdit={handleEditItem}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-3">
            {processedItems.map((item) => (
              <WishlistItemRow
                key={item.id}
                item={item}
                itemOwner={currentUser} // On this page, currentUser is always the itemOwner
                isOwnList={true}
                onEdit={handleEditItem}
              />
            ))}
          </div>
        )
      ) : (
        <Alert className="border-primary/50 text-primary bg-primary/5">
          <Info className="h-5 w-5 text-primary" />
          <AlertTitle className="font-semibold">
            {selectedCategoryIds.length > 0 ? 'No Items Match Selected Categories' : 'Your Wishlist is Empty!'}
          </AlertTitle>
          <AlertDescription>
            {selectedCategoryIds.length > 0
              ? `No items found for the selected categories. Try clearing the filter or adding new items in these categories.`
              : 'Start adding items you\'d love to receive. Click the "Add New Item" button to begin.'}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

