
"use client";

import { useEffect, useState } from 'react';
import { useWishlist } from '@/contexts/WishlistContext';
import type { Message, User } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Bell, Info, PackagePlus, FileEdit, Trash2, UserCheck, ShieldCheck, MessageSquareQuote, Settings2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function NotificationsPage() {
  const { currentUser, getMessagesForUser, markMessagesAsRead, deleteAllNotificationsForUser, users } = useWishlist();
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (currentUser) {
      markMessagesAsRead(currentUser.id);
    }
  }, [currentUser, markMessagesAsRead]);

  if (!currentUser) {
    return <p>Loading user data or please log in...</p>;
  }

  const messages = getMessagesForUser(currentUser.id);

  const handleDeleteAllNotifications = async () => {
    if (!currentUser) return;
    
    setIsDeleting(true);
    try {
      const success = await deleteAllNotificationsForUser(currentUser.id);
      if (success) {
        // The context will automatically update the messages state
        console.log("All notifications deleted successfully");
      }
    } catch (error) {
      console.error("Error deleting notifications:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const getInitials = (name: string) => {
    const names = name.split(' ');
    if (names.length === 1) return names[0][0].toUpperCase();
    return names[0][0].toUpperCase() + names[names.length - 1][0].toUpperCase();
  };

  const getEventIcon = (eventType: Message['eventType']) => {
    switch (eventType) {
      case 'item_added':
        return <PackagePlus className="h-5 w-5 text-green-500" />;
      case 'item_updated':
      case 'item_updated_by_admin':
        return <FileEdit className="h-5 w-5 text-blue-500" />;
      case 'item_deleted_by_owner':
      case 'item_deleted_by_admin':
        return <Trash2 className="h-5 w-5 text-red-500" />;
      case 'item_commitment_impacted_by_deletion':
        return <UserCheck className="h-5 w-5 text-orange-500" />;
      case 'system_notification_reset':
        return <Settings2 className="h-5 w-5 text-purple-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const getFormattedTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      return "Invalid date";
    }
  };

  const formatChanges = (changes?: Message['changes']) => {
    if (!changes || changes.length === 0) return null;
    return (
      <ul className="list-disc list-inside text-xs text-muted-foreground mt-1 pl-2 space-y-0.5">
        {changes.map((change, index) => (
          <li key={index}>
            <span className="font-medium capitalize">{change.field.replace(/([A-Z])/g, ' $1')}:</span> changed from "{change.oldValue || 'empty'}" to "{change.newValue || 'empty'}"
          </li>
        ))}
      </ul>
    );
  };

  const getActingUser = (actingUserId?: string): User | undefined => {
    if (!actingUserId) return undefined;
    return users.find(u => u.id === actingUserId);
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
        </div>
        {messages.length > 0 && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" size="sm" disabled={isDeleting}>
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleting ? "Deleting..." : "Delete All"}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete All Notifications</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete all your notifications? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDeleteAllNotifications} disabled={isDeleting}>
                  {isDeleting ? "Deleting..." : "Delete All"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </div>

      {messages.length > 0 ? (
        <div className="space-y-4">
          {messages.map((msg) => {
            const actingUser = getActingUser(msg.actingUserId);
            return (
            <Card key={msg.id} className={`border-l-4 ${msg.isRead ? 'border-border' : 'border-primary animate-pulse-once'}`}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                        {getEventIcon(msg.eventType)}
                        {actingUser && (
                           <Avatar className="h-6 w-6">
                              <AvatarImage src={actingUser.avatarUrl} alt={actingUser.name} data-ai-hint="person avatar" />
                              <AvatarFallback>{getInitials(actingUser.name)}</AvatarFallback>
                            </Avatar>
                        )}
                        <CardTitle className="text-base font-medium leading-tight">
                            {msg.eventType === 'item_added' && `${msg.actingUserName || 'Someone'} added an item`}
                            {msg.eventType === 'item_updated' && `${msg.actingUserName || 'Someone'} updated an item`}
                            {msg.eventType === 'item_deleted_by_owner' && `${msg.actingUserName || 'Someone'} deleted an item`}
                            {msg.eventType === 'item_commitment_impacted_by_deletion' && `Alert: Item you committed to was impacted`}
                            {msg.eventType === 'item_updated_by_admin' && `Admin ${msg.actingUserName || ''} updated an item`}
                            {msg.eventType === 'item_deleted_by_admin' && `Admin ${msg.actingUserName || ''} deleted an item`}
                            {msg.eventType === 'system_notification_reset' && `System: Notification History Reset`}
                        </CardTitle>
                    </div>
                    <span className="text-xs text-muted-foreground whitespace-nowrap">
                        {getFormattedTimestamp(msg.timestamp)}
                    </span>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-foreground/90">{msg.content}</p>
                {msg.eventType === 'item_updated_by_admin' && msg.changes && formatChanges(msg.changes)}
                {msg.eventType === 'item_deleted_by_admin' && msg.adminReason && (
                  <div className="mt-1 text-xs text-muted-foreground italic flex items-center">
                     <MessageSquareQuote className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                    Admin Reason: {msg.adminReason}
                  </div>
                )}
                {msg.itemInfo?.id && msg.actingUserId && (msg.eventType === 'item_added' || msg.eventType === 'item_updated' || msg.eventType === 'item_updated_by_admin') && (
                  <Button variant="link" size="sm" asChild className="p-0 h-auto mt-1 text-xs">
                    <Link href={`/users/${msg.itemInfo.ownerName ? users.find(u=>u.name === msg.itemInfo.ownerName)?.id : msg.actingUserId}/wishlist#item-${msg.itemInfo.id}`}>
                      View Wishlist Item
                    </Link>
                  </Button>
                )}
                 {msg.eventType === 'item_commitment_impacted_by_deletion' && (
                    <Button variant="link" size="sm" asChild className="p-0 h-auto mt-1 text-xs">
                        <Link href="/my-reservations">
                            View My Commitments
                        </Link>
                    </Button>
                 )}
              </CardContent>
            </Card>
          )})}
        </div>
      ) : (
        <Alert>
          <Info className="h-5 w-5" />
          <AlertTitle>No Notifications</AlertTitle>
          <AlertDescription>You currently have no new notifications.</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
