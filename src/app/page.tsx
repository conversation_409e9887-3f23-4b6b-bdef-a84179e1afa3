
"use client";

import Link from 'next/link';
import { useState, useMemo, useEffect } from 'react';
import { useWishlist } from '@/contexts/WishlistContext';
import { WishlistItemCard } from '@/components/wishlists/WishlistItemCard';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ArrowRight, ListChecks, PlusCircle, Users, Eye, Info, ShoppingBag, CalendarDays, CalendarPlus, AlertTriangle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription as AlertDescriptionComponent, AlertTitle } from "@/components/ui/alert";
import type { WishlistItem as WishlistItemType, UpcomingEventDisplayInfo, User as AppUserType, Event as EventType } from '@/lib/types';
import {
  Dialog,
  DialogContent,
  DialogDescription as DialogDescriptionComponentDialog,
  DialogHeader,
  DialogTitle as DialogTitleComponent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { format, addDays } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { AddEventForm } from '@/components/events/AddEventForm';
import { AddWishlistItemForm } from '@/components/wishlists/AddWishlistItemForm';
import { cn } from '@/lib/utils';


export default function DashboardPage() {
  const { currentUser, users: contextUsers, getUpcomingEventsForUser, firebaseUser, events: contextEvents, getItemsForUser, authLoading } = useWishlist();
  const [isItemFormOpen, setIsItemFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<WishlistItemType | null>(null);
  const [isEventFormOpen, setIsEventFormOpen] = useState(false);

  // console.log("[DashboardPage] Rendering. CurrentUser ID:", currentUser?.id, "FirebaseUser UID:", firebaseUser?.uid);

  const currentUserItems = useMemo(() => {
    if (!currentUser || authLoading) return [];
    return getItemsForUser(currentUser.id);
  }, [currentUser, getItemsForUser, authLoading]);


  const upcomingEvents = useMemo(() => {
    // console.log(`[DashboardPage useMemo upcomingEvents] Calculating. currentUser exists: ${!!currentUser}. Context events length: ${contextEvents?.length}`);
    // console.log(`[DashboardPage useMemo upcomingEvents] contextEvents:`, contextEvents);
    // console.log(`[DashboardPage useMemo upcomingEvents] currentUser:`, currentUser);
    if (!currentUser || !contextEvents) {
      return [];
    }
    const eventsResult = getUpcomingEventsForUser(currentUser.id);
    // console.log(`[DashboardPage useMemo upcomingEvents] Calculated upcomingEvents: ${eventsResult.length}`, eventsResult.map(e => ({name: e.name, date: e.displayDate, type: e.type})));
    return eventsResult;
  }, [currentUser, getUpcomingEventsForUser, contextEvents]);


  const friends = useMemo(() => {
    // console.log(`[DashboardPage useMemo friends] Calculating. currentUser exists: ${!!currentUser}. Context users length: ${contextUsers.length}. Context wishlists length: ${contextWishlists?.length}`);
    // console.log(`[DashboardPage useMemo friends] contextUsers:`, contextUsers);
    // console.log(`[DashboardPage useMemo friends] contextWishlists:`, contextWishlists);
    if (!currentUser || !contextUsers.length) {
      return [];
    }
    const currentUserGroups = currentUser.groups || [];
    const filteredFriends = contextUsers.filter(user => {
      if (user.id === currentUser.id) return false;
      if (!user.isApproved) return false;
      const userGroups = user.groups || [];
      return currentUserGroups.some(cg => userGroups.includes(cg));
    });
    // console.log("[DashboardPage useMemo friends] Calculated friends:", filteredFriends.map(f => ({id: f.id, name: f.name, groups: f.groups?.join(',')})));
    return filteredFriends;
  }, [currentUser, contextUsers]);


  if (!firebaseUser && !authLoading) {
    // console.log("[DashboardPage] No firebaseUser, rendering login prompt.");
    // This case should ideally be handled by AuthGuard redirecting to /login
    return <p>Please log in to view the dashboard.</p>;
  }

  if (authLoading) {
     return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex flex-col items-center">
            <Loader2 className="animate-spin h-10 w-10 text-primary mb-4" />
            <p className="text-lg text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      );
  }
  
  if (!currentUser && firebaseUser) {
    // console.log("[DashboardPage] firebaseUser exists, but no currentUser (AppUser). Showing profile incomplete / loading message.");
    return (
      <div className="space-y-6 flex flex-col items-center justify-center min-h-[60vh]">
          <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
          <h1 className="text-2xl font-bold text-destructive">Profile Incomplete or Loading</h1>
          <p className="text-muted-foreground mt-2 text-center max-w-md">
            You are logged in as {firebaseUser.email}. We are loading your GiftLink profile or it may need to be set up by an administrator.
          </p>
           <Alert variant="default" className="mt-4 bg-yellow-50 border-yellow-300 text-yellow-700 max-w-lg">
            <Info className="h-5 w-5 !text-yellow-600" />
            <AlertTitle className="font-semibold">Application Note</AlertTitle>
            <AlertDescriptionComponent>
              User profiles are linked to Firestore. If this is your first login and your profile hasn't been created/loaded from Firestore, some features may be limited.
            </AlertDescriptionComponent>
          </Alert>
           <Loader2 className="animate-spin h-8 w-8 text-primary mt-4" />
      </div>
    )
  }

  if (!currentUser) { // Should not be reached if authLoading is false and firebaseUser exists, but as a fallback
    //  console.log("[DashboardPage] currentUser is null after firebaseUser check. Showing main loading screen.");
     return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex flex-col items-center">
            <Loader2 className="animate-spin h-10 w-10 text-primary mb-4" />
            <p className="text-lg text-muted-foreground">Loading user data...</p>
          </div>
        </div>
      );
  }

  const getInitials = (name: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length === 1) return names[0][0] ? names[0][0].toUpperCase() : '';
    return (names[0][0] ? names[0][0].toUpperCase() : '') + (names[names.length - 1][0] ? names[names.length - 1][0].toUpperCase() : '');
  }

  const handleEditItem = (item: WishlistItemType) => {
    setEditingItem(item);
    setIsItemFormOpen(true);
  };

  const handleItemFormSubmit = () => {
    setIsItemFormOpen(false);
    setEditingItem(null);
  };

  const handleEventFormSubmit = () => {
    setIsEventFormOpen(false);
  };

  const formatEventDate = (event: UpcomingEventDisplayInfo) => {
    if (event.isRecurringYearly) {
      return format(event.displayDate, "MMMM d");
    }
    return format(event.displayDate, "MMMM d, yyyy");
  };

  const formatDateForICS = (date: Date, includeTime: boolean = false): string => {
    const year = date.getUTCFullYear();
    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = date.getUTCDate().toString().padStart(2, '0');
    if (includeTime) {
      const hours = date.getUTCHours().toString().padStart(2, '0');
      const minutes = date.getUTCMinutes().toString().padStart(2, '0');
      const seconds = date.getUTCSeconds().toString().padStart(2, '0');
      return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
    }
    return `${year}${month}${day}`;
  };

  const generateICSContent = (event: UpcomingEventDisplayInfo): string => {
    const dtstamp = formatDateForICS(new Date(), true);
    const dtstart = formatDateForICS(event.displayDate);
    const dtend = formatDateForICS(addDays(event.displayDate, 1));

    let icsString = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//GiftLink//App//EN',
      'BEGIN:VEVENT',
      `UID:giftlink-event-${event.id}@giftlink.app`,
      `DTSTAMP:${dtstamp}`,
      `DTSTART;VALUE=DATE:${dtstart}`,
      `DTEND;VALUE=DATE:${dtend}`,
      `SUMMARY:${event.name.replace(/[^\w\s]/gi, '')}`,
    ];

    if (event.description) {
      icsString.push(`DESCRIPTION:${event.description.replace(/[^\w\s.,!?']/gi, '')}`);
    }

    if (event.isRecurringYearly) {
      icsString.push('RRULE:FREQ=YEARLY');
    }

    icsString.push('END:VEVENT');
    icsString.push('END:VCALENDAR');

    return icsString.join('\r\n');
  };

  const downloadICSFile = (event: UpcomingEventDisplayInfo) => {
    const icsContent = generateICSContent(event);
    const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8;' });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    const filename = `${event.name.replace(/\s+/g, '_').replace(/[^\w-]/gi, '')}.ics`;
    link.setAttribute("download", filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };


  return (
    <div className="space-y-12">
      <section className="p-6 bg-gradient-to-r from-primary/80 to-accent/80 text-primary-foreground rounded-xl shadow-lg">
        <h1 className="text-4xl font-bold">Welcome back, {currentUser.name}!</h1>
        <p className="mt-2 text-lg opacity-90">Ready to manage your gifts and see what your friends are wishing for?</p>
      </section>

      <section className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <CalendarDays className="h-6 w-6 text-primary" /> Upcoming Events
          </h2>
          {currentUser.isApproved && (
            <Dialog open={isEventFormOpen} onOpenChange={setIsEventFormOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <PlusCircle className="mr-2 h-4 w-4" /> Create Event
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitleComponent>Create New Event</DialogTitleComponent>
                  <DialogDescriptionComponentDialog>
                    Add a new event and associate it with your groups.
                  </DialogDescriptionComponentDialog>
                </DialogHeader>
                <AddEventForm onFormSubmit={handleEventFormSubmit} />
              </DialogContent>
            </Dialog>
          )}
        </div>
        {upcomingEvents.length > 0 ? (
           <div className="space-y-3">
            {upcomingEvents.map((event) => (
              <Card key={event.id} className="shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between p-3 sm:p-4">
                  <div className="flex items-start flex-grow min-w-0">
                    <div className="w-36 sm:w-40 flex-shrink-0 pr-2 sm:pr-3 pt-px">
                      <span className="text-sm font-medium text-primary whitespace-nowrap">
                        {formatEventDate(event)}
                      </span>
                    </div>
                    <div className="flex-grow min-w-0 ml-1 sm:ml-2">
                      <div className="flex items-baseline gap-1.5 sm:gap-2 flex-wrap">
                        <h3 className="font-semibold text-sm sm:text-base leading-tight truncate" title={event.name}>
                          {event.name}
                        </h3>
                        {event.isRecurringYearly && (
                          <Badge variant="outline" className="text-xs px-1.5 py-0 sm:px-2 whitespace-nowrap">
                            Recurring
                          </Badge>
                        )}
                      </div>
                      {event.description && (
                        <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5" title={event.description}>
                          {event.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0 ml-2 sm:ml-4">
                    <Badge variant={event.type === 'system' ? 'secondary' : 'default'} className="text-xs px-1.5 py-0 sm:px-2 whitespace-nowrap">
                      {event.type === 'system' ? 'System' : 'Group'}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => downloadICSFile(event)}
                      aria-label={`Export ${event.name} to calendar`}
                      className="text-primary hover:text-primary/80 h-7 px-1.5 sm:h-8 sm:px-2"
                    >
                      <CalendarPlus className="h-3 w-3 sm:h-3.5 sm:w-3.5 sm:mr-1" />
                      <span className="hidden sm:inline text-xs">Calendar</span>
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Alert className="border-primary/50 text-primary bg-primary/5">
            <Info className="h-5 w-5 text-primary" />
            <AlertTitle className="font-semibold">No Upcoming Events</AlertTitle>
            <AlertDescriptionComponent>
              There are no events in the next few months for your groups or system-wide.
              {currentUser.isApproved && " You can create a new group event!"}
            </AlertDescriptionComponent>
          </Alert>
        )}
      </section>

      <section className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <ListChecks className="h-6 w-6 text-primary" /> My Wishlist Snapshot
          </h2>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button asChild variant="outline" className="w-full sm:w-auto">
              <Link href="/my-reservations">
                <ShoppingBag className="mr-2 h-4 w-4" /> View My Commitments
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full sm:w-auto">
              <Link href="/my-wishlist">
                Manage My Wishlist <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
        {authLoading ? (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="ml-2 text-muted-foreground">Loading your wishlist snapshot...</p>
          </div>
        ) : currentUserItems.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentUserItems.slice(0, 3).map(item => (
              <WishlistItemCard
                key={item.id}
                item={item}
                itemOwner={currentUser}
                isOwnList={true}
                onEdit={handleEditItem}
              />
            ))}
          </div>
        ) : (
          <Alert className="border-primary/50 text-primary bg-primary/5">
            <Info className="h-5 w-5 text-primary" />
            <AlertTitle className="font-semibold">Your Wishlist is Empty!</AlertTitle>
            <AlertDescriptionComponent className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <span>Start adding items you'd love to receive.</span>
              <Dialog open={isItemFormOpen && !editingItem} onOpenChange={(open) => { if (!open && !editingItem) setIsItemFormOpen(false); }}>
                <DialogTrigger asChild>
                   <Button size="sm" className="mt-2 sm:mt-0" onClick={() => { setEditingItem(null); setIsItemFormOpen(true); }}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Item
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitleComponent>Add New Item to Your Wishlist</DialogTitleComponent>
                    <DialogDescriptionComponentDialog>
                      Fill in the details of the item you wish to add.
                    </DialogDescriptionComponentDialog>
                  </DialogHeader>
                  <AddWishlistItemForm itemToEdit={null} onFormSubmit={handleItemFormSubmit} />
                </DialogContent>
              </Dialog>
            </AlertDescriptionComponent>
          </Alert>
        )}
        {!authLoading && currentUserItems.length > 3 && (
          <div className="text-center mt-4">
            <Button asChild variant="link">
              <Link href="/my-wishlist">
                View All My Items ({currentUserItems.length}) <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        )}
      </section>

      <section className="space-y-6">
        <h2 className="text-2xl font-semibold flex items-center gap-2">
          <Users className="h-6 w-6 text-accent" /> Friends' Wishlists
        </h2>
        {friends.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {friends.slice(0, 6).map(friend => {
              const friendItems = getItemsForUser(friend.id);
              // console.log(`[DashboardPage] Rendering Friend: ${friend.name}, Item count: ${friendItems.length}`);
              return (
                <Card key={friend.id} className="shadow-md hover:shadow-lg transition-shadow">
                  <CardHeader className="flex flex-row items-center gap-3 space-y-0 pb-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={friend.avatarUrl} alt={friend.name} data-ai-hint="person avatar" />
                      <AvatarFallback>{getInitials(friend.name)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">{friend.name}</CardTitle>
                      <CardDescription className="text-xs">
                        {friendItems.length} item(s) on wishlist
                      </CardDescription>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {friendItems.length > 0 ? (
                      <ul className="text-xs space-y-1 text-muted-foreground">
                        {friendItems.slice(0, 2).map(item => (
                          <li key={item.id} className="truncate"> • {item.name}</li>
                        ))}
                        {friendItems.length > 2 && <li>... and more</li>}
                      </ul>
                    ) : (
                      <p className="text-xs text-muted-foreground">No items on their public wishlist yet.</p>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button asChild size="sm" variant="outline" className="w-full">
                      <Link href={`/users/${friend.id}/wishlist`}>
                        <Eye className="mr-2 h-4 w-4" /> View {friend.name.split(' ')[0]}'s List
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        ) : (
           <Alert className="border-accent/50 text-accent bg-accent/5">
            <Info className="h-5 w-5 text-accent" />
            <AlertTitle className="font-semibold">No Friends To Display</AlertTitle>
            <AlertDescriptionComponent>
              No approved users in your groups have wishlists, or you haven't joined any groups with other approved users. Check if users in your groups have created wishlists.
            </AlertDescriptionComponent>
          </Alert>
        )}
         {friends.length > 6 && (
          <div className="text-center mt-4">
            <Button asChild variant="link">
              {/* Link to a future "All Friends" page or similar */}
              <span className="cursor-not-allowed">View All Friends' Wishlists ({friends.length})</span>
            </Button>
          </div>
        )}
      </section>

      <Dialog open={isItemFormOpen && !!editingItem} onOpenChange={(open) => { if (!open && editingItem) { setEditingItem(null); setIsItemFormOpen(false); } else { setIsItemFormOpen(open); } }}>
        <DialogContent className="sm:max-w-[425px] md:max-w-lg lg:max-w-xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitleComponent>Edit Item</DialogTitleComponent>
            <DialogDescriptionComponentDialog>
              Update the details of your wishlist item.
            </DialogDescriptionComponentDialog>
          </DialogHeader>
          <AddWishlistItemForm itemToEdit={editingItem} onFormSubmit={handleItemFormSubmit} />
        </DialogContent>
      </Dialog>
    </div>
  );
}

