
"use client";

import { useEffect, useState, use, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useWishlist } from '@/contexts/WishlistContext';
import { WishlistItemCard } from '@/components/wishlists/WishlistItemCard';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserCircle, Gift, Info, LayoutGrid, ListFilter, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { WishlistItemRow } from '@/components/wishlists/WishlistItemRow';
import { cn } from '@/lib/utils';
import type { WishlistItem, User, Wishlist as WishlistType } from '@/lib/types';

interface UserWishlistPageProps {
  params: Promise<{ userId: string }>;
}

type ViewMode = 'grid' | 'list';

export default function UserWishlistPage({ params: paramsPromise }: UserWishlistPageProps) {
  const resolvedParams = use(paramsPromise);
  const { userId } = resolvedParams;

  const { users, getWishlistByUserId, getItemsForUser, currentUser, authLoading: contextAuthLoading } = useWishlist();
  const router = useRouter();
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  const [itemOwner, setItemOwner] = useState<User | undefined>(undefined);
  const [wishlistShell, setWishlistShell] = useState<WishlistType | undefined>(undefined); // Stores the wishlist shell
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isActive = true;
    const loadPageData = async () => {
      if (!userId) {
        if (isActive) {
          setError("User ID is missing.");
          setIsLoading(false);
        }
        return;
      }
      if (isActive) {
        setIsLoading(true);
        setError(null);
      }
      try {
        // Fetch wishlist shell to ensure the user has a wishlist record
        const shell = await getWishlistByUserId(userId);
        if (isActive) {
          setWishlistShell(shell);
          // Attempt to find the owner from the main users list in context
          const ownerProfile = users.find(u => u.id === userId);
          setItemOwner(ownerProfile);

          if (!shell && !ownerProfile) {
            // If neither wishlist shell nor user profile exists, it's likely a non-existent user
            setError("Wishlist or user not found.");
          } else if (!ownerProfile && shell) {
            // Wishlist shell exists but owner not in current users list (might be an edge case, or data is still loading)
            console.warn(`[UserWishlistPage] Wishlist shell for ${userId} found, but owner profile not in users state yet.`);
          }
        }
      } catch (err) {
        console.error("[UserWishlistPage] Error loading page data:", err);
        if (isActive) setError("Failed to load wishlist data.");
      } finally {
        if (isActive) setIsLoading(false);
      }
    };

    if (!contextAuthLoading) { // Only run after initial auth check
        loadPageData();
    } else {
        setIsLoading(true); // Keep loading if context is still auth loading
    }
    return () => { isActive = false; };
  }, [userId, getWishlistByUserId, users, contextAuthLoading]);

  // Update itemOwner if users list changes (e.g. after full data load)
  useEffect(() => {
    if (userId && !itemOwner) {
      const ownerProfile = users.find(u => u.id === userId);
      if (ownerProfile) {
        setItemOwner(ownerProfile);
      }
    }
  }, [users, userId, itemOwner]);


  useEffect(() => {
    if (!isLoading && !contextAuthLoading && currentUser && userId === currentUser.id) {
      router.replace('/my-wishlist');
    }
  }, [currentUser, userId, router, isLoading, contextAuthLoading]);


  const userItems = useMemo(() => {
    if (!userId || contextAuthLoading) return [];
    return getItemsForUser(userId);
  }, [userId, getItemsForUser, contextAuthLoading]);

  const displayableItems = useMemo(() => {
    return userItems.filter(
      (item: WishlistItem) => (item.receivedQuantity || 0) < item.quantity
    );
  }, [userItems]);

  if (isLoading || contextAuthLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="flex flex-col items-center">
          <Loader2 className="animate-spin h-8 w-8 text-primary mb-4" />
          <p className="text-muted-foreground">Loading wishlist for user: {userId || 'Unknown'}...</p>
        </div>
      </div>
    );
  }

  if (error) {
     return (
      <div className="text-center py-10">
        <UserCircle className="mx-auto h-12 w-12 text-destructive" />
        <h2 className="mt-4 text-xl font-semibold text-destructive">Error Loading Wishlist</h2>
        <p className="mt-2 text-muted-foreground">{error}</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  if (!itemOwner) { // If itemOwner is still not found after loading attempts
    return (
      <div className="text-center py-10">
        <UserCircle className="mx-auto h-12 w-12 text-muted-foreground" />
        <h2 className="mt-4 text-xl font-semibold">User Profile ({userId || 'Unknown'}) Not Found</h2>
        <p className="mt-2 text-muted-foreground">The user profile you are looking for does not exist or you may not have permission to view it.</p>
        <Button asChild className="mt-6">
          <Link href="/">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }
  
  // This explicit redirect check for self-view might be redundant if useEffect above handles it robustly
  // but can serve as a fallback message while redirect is processing.
  if (currentUser && userId === currentUser.id) {
    return (
        <div className="flex items-center justify-center min-h-[50vh]">
            <div className="flex flex-col items-center">
                <Loader2 className="animate-spin h-8 w-8 text-primary mb-4" />
                <p className="text-muted-foreground">Redirecting to your wishlist...</p>
            </div>
        </div>
    );
  }

  const getInitials = (name: string) => {
    if (!name) return '';
    const names = name.split(' ');
    if (names.length === 1) return names[0][0] ? names[0][0].toUpperCase() : '';
    return (names[0][0] ? names[0][0].toUpperCase() : '') + (names[names.length - 1][0] ? names[names.length - 1][0].toUpperCase() : '');
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 border border-border rounded-lg bg-card shadow-sm">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={itemOwner.avatarUrl} alt={itemOwner.name} data-ai-hint="person avatar" />
            <AvatarFallback>{getInitials(itemOwner.name)}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Gift className="h-7 w-7 text-primary" />
              {itemOwner.name}'s Wishlist
            </h1>
            {itemOwner.displayEmailToGroupMembers && (
              <p className="text-muted-foreground">
                {itemOwner.email}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2 self-start sm:self-center mt-2 sm:mt-0">
            <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
                aria-label="Grid view"
            >
                <LayoutGrid className="h-5 w-5" />
            </Button>
            <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
                aria-label="List view"
            >
                <ListFilter className="h-5 w-5" />
            </Button>
        </div>
      </div>

      {displayableItems.length > 0 ? (
        viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {displayableItems.map((item) => (
              <WishlistItemCard
                key={item.id}
                item={item}
                itemOwner={itemOwner}
                isOwnList={false}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-3">
            {displayableItems.map((item) => (
              <WishlistItemRow
                key={item.id}
                item={item}
                itemOwner={itemOwner}
                isOwnList={false}
              />
            ))}
          </div>
        )
      ) : (
         <Alert className="border-accent/50 text-accent bg-accent/5">
          <Info className="h-5 w-5 text-accent" />
          <AlertTitle className="font-semibold">{itemOwner.name}'s Wishlist is Empty or All Items Received</AlertTitle>
          <AlertDescription>
            This user hasn't added any giftable items to their wishlist yet, or all items have been marked as received.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
