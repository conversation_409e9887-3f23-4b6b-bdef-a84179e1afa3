import React, { useEffect, useState } from 'react';
import { PasswordDialog } from './PasswordDialog';
import { passwordDialogEvents } from '@/services/wishlistAuthService';

export function PasswordDialogProvider() {
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [callback, setCallback] = useState<((password: string | null) => void) | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  useEffect(() => {
    const removeListener = passwordDialogEvents.addListener((request) => {
      setTitle(request.title);
      setDescription(request.description);
      setCallback(() => request.callback);
      setErrorMessage(undefined);
      setIsOpen(true);
    });

    return removeListener;
  }, []);

  const handleClose = () => {
    if (callback) {
      callback(null);
    }
    setIsOpen(false);
    setCallback(null);
    setErrorMessage(undefined);
  };

  const handleConfirm = async (password: string) => {
    if (callback) {
      try {
        callback(password);
        setIsOpen(false);
        setCallback(null);
        setErrorMessage(undefined);
      } catch (error) {
        setErrorMessage("Invalid password. Please try again.");
      }
    }
  };

  return (
    <PasswordDialog
      isOpen={isOpen}
      title={title}
      description={description}
      onClose={handleClose}
      onConfirm={handleConfirm}
      errorMessage={errorMessage}
    />
  );
}