
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { WishlistItem, Category, Rank } from "@/lib/types";
import { useWishlist } from "@/contexts/WishlistContext";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState, useRef } from "react";
import Image from "next/image";
import { X } from "lucide-react";

const MAX_ITEM_IMAGE_DIMENSION = 800; // Max width/height in pixels

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(100),
  price: z.coerce.number().positive("Price must be a positive number."),
  retailer: z.string().min(2, "Retailer must be at least 2 characters.").max(50),
  url: z.string().url("Please enter a valid URL.").optional().or(z.literal('')),
  imageUrl: z.string().optional(), // Data URL or empty
  description: z.string().max(500, "Description cannot exceed 500 characters.").optional(),
  categoryId: z.string().optional(),
  rankId: z.string().optional(),
  quantity: z.coerce.number().int().min(1, "Quantity must be at least 1.").default(1),
});

type AddWishlistItemFormValues = z.infer<typeof formSchema>;

interface AddWishlistItemFormProps {
  itemToEdit?: WishlistItem | null;
  onFormSubmit: () => void;
  isAdminEdit?: boolean;
  onAdminSubmit?: (updatedItemData: Partial<Omit<WishlistItem, 'id' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => Promise<boolean | void>;
}

const SELECT_NONE_VALUE = "_SELECT_NONE_";

export function AddWishlistItemForm({
  itemToEdit,
  onFormSubmit,
  isAdminEdit = false,
  onAdminSubmit
}: AddWishlistItemFormProps) {
  const { addItemToCurrentUserWishlist, updateItemInCurrentUserWishlist, categories, ranks, currentUser } = useWishlist();
  const { toast } = useToast();
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<AddWishlistItemFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      price: 0,
      retailer: "",
      url: "",
      imageUrl: undefined,
      description: "",
      categoryId: undefined,
      rankId: undefined,
      quantity: 1,
    },
  });

  useEffect(() => {
    if (itemToEdit) {
      form.reset({
        name: itemToEdit.name,
        price: itemToEdit.price,
        retailer: itemToEdit.retailer,
        url: itemToEdit.url || "",
        imageUrl: itemToEdit.imageUrl || undefined,
        description: itemToEdit.description || "",
        categoryId: itemToEdit.categoryId || undefined,
        rankId: itemToEdit.rankId || undefined,
        quantity: itemToEdit.quantity || 1,
      });
      setImagePreview(itemToEdit.imageUrl || null);
    } else {
       form.reset({
        name: "", price: 0, retailer: "", url: "", imageUrl: undefined,
        description: "", categoryId: undefined, rankId: undefined, quantity: 1,
      });
      setImagePreview(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemToEdit, form.reset]);

  const handleImageFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const originalDataUrl = reader.result as string;
        
        const img = document.createElement('img');
        img.onload = () => {
          let { width, height } = img;
          if (width > MAX_ITEM_IMAGE_DIMENSION || height > MAX_ITEM_IMAGE_DIMENSION) {
            if (width > height) {
              height = Math.round((height / width) * MAX_ITEM_IMAGE_DIMENSION);
              width = MAX_ITEM_IMAGE_DIMENSION;
            } else {
              width = Math.round((width / height) * MAX_ITEM_IMAGE_DIMENSION);
              height = MAX_ITEM_IMAGE_DIMENSION;
            }
          }
          
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(img, 0, 0, width, height);
            const resizedDataUrl = canvas.toDataURL('image/webp', 0.85);
            setImagePreview(resizedDataUrl);
            form.setValue('imageUrl', resizedDataUrl, { shouldValidate: true });
          } else {
            setImagePreview(originalDataUrl);
            form.setValue('imageUrl', originalDataUrl, { shouldValidate: true });
          }
        };
        img.onerror = () => {
          toast({ title: "Image Error", description: "Could not load the selected image file.", variant: "destructive" });
          clearImage();
        };
        img.src = originalDataUrl;
      };
      reader.readAsDataURL(file);
    } else {
      const originalImageUrl = itemToEdit?.imageUrl || undefined;
      setImagePreview(originalImageUrl || null);
      form.setValue('imageUrl', originalImageUrl, { shouldValidate: true });
    }
  };

  const clearImage = () => {
    setImagePreview(null);
    form.setValue('imageUrl', "", { shouldValidate: true });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  async function onSubmit(values: AddWishlistItemFormValues) {
    // Construct the payload by only including fields that have meaningful values
    const payloadForFirestore: Partial<Omit<WishlistItem, 'id' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>> = {
        name: values.name,
        price: values.price,
        retailer: values.retailer,
        quantity: values.quantity || 1, // Ensure quantity has a default
    };

    if (values.url && values.url.trim() !== "") {
        payloadForFirestore.url = values.url;
    }
    
    // Handle image URL - always include it when editing to allow removal
    if (itemToEdit) {
        // When editing, always include imageUrl field (undefined signals removal)
        payloadForFirestore.imageUrl = (values.imageUrl && values.imageUrl.trim() !== "") ? values.imageUrl : undefined;
    } else if (values.imageUrl && values.imageUrl.trim() !== "") {
        // When adding new item, only include if there's actually an image
        payloadForFirestore.imageUrl = values.imageUrl;
    }
    
    if (values.description && values.description.trim() !== "") {
        payloadForFirestore.description = values.description;
    }
    if (values.categoryId && values.categoryId !== SELECT_NONE_VALUE) {
        payloadForFirestore.categoryId = values.categoryId;
    }
    if (values.rankId && values.rankId !== SELECT_NONE_VALUE) {
        payloadForFirestore.rankId = values.rankId;
    }

    let success = false;
    let toastMessage = "";

    if (isAdminEdit && onAdminSubmit && itemToEdit) {
      const result = await onAdminSubmit(payloadForFirestore);
      success = typeof result === 'boolean' ? result : true; // Admin submit toast handled by admin page
    } else if (itemToEdit && currentUser) {
      success = await updateItemInCurrentUserWishlist(itemToEdit.id, payloadForFirestore);
      toastMessage = success ? `"${values.name}" has been updated.` : "Could not update the item.";
       toast({
        title: success ? "Item Updated" : "Update Failed",
        description: toastMessage,
        variant: success ? "default" : "destructive",
      });
    } else if (currentUser) {
      // Ensure no fields are undefined when casting for addItem
      const newItemData = payloadForFirestore as Omit<WishlistItem, 'id' | 'ownerUid'  | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>;
      success = await addItemToCurrentUserWishlist(newItemData);
      toastMessage = success ? `"${values.name}" has been added to your wishlist.` : "Could not add the item.";
       toast({
        title: success ? "Item Added" : "Add Failed",
        description: toastMessage,
        variant: success ? "default" : "destructive",
      });
    }

    if (success) {
        onFormSubmit();
        if (!itemToEdit || !isAdminEdit) { 
             form.reset({
                name: "", price: 0, retailer: "", url: "", imageUrl: undefined,
                description: "", categoryId: undefined, rankId: undefined, quantity: 1,
            });
            setImagePreview(null);
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Item Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Coffee Maker" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Price ($)</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" placeholder="e.g., 49.99" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="retailer"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Retailer</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Amazon, Local Store" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Item URL (Optional)</FormLabel>
              <FormControl>
                <Input type="url" placeholder="https://example.com/product" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="categoryId"
            render={({ field }) => (
              <FormItem key={itemToEdit ? `category-${itemToEdit.id}-${itemToEdit.categoryId}` : 'new-category'}>
                <FormLabel>Category (Optional)</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value === SELECT_NONE_VALUE ? undefined : value);
                  }}
                  value={field.value}
                  defaultValue={itemToEdit?.categoryId || undefined}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={SELECT_NONE_VALUE}>None</SelectItem>
                    {categories.map((category: Category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="rankId"
            render={({ field }) => (
              <FormItem key={itemToEdit ? `rank-${itemToEdit.id}-${itemToEdit.rankId}` : 'new-rank'}>
                <FormLabel>Ranking (Optional)</FormLabel>
                 <Select
                    onValueChange={(value) => {
                        field.onChange(value === SELECT_NONE_VALUE ? undefined : value);
                    }}
                    value={field.value}
                    defaultValue={itemToEdit?.rankId || undefined}
                  >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a rank" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={SELECT_NONE_VALUE}>None</SelectItem>
                    {ranks.sort((a,b) => a.order - b.order).map((rank: Rank) => (
                      <SelectItem key={rank.id} value={rank.id}>
                        {rank.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
         <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity</FormLabel>
                <FormControl>
                  <Input type="number" min="1" step="1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

        <FormItem>
          <FormLabel>Item Image</FormLabel>
          <FormControl>
            <Input
              type="file"
              accept="image/png, image/jpeg, image/gif, image/webp"
              ref={fileInputRef}
              onChange={handleImageFileChange}
              className="text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
            />
          </FormControl>
          {imagePreview && (
            <div className="mt-3 relative w-36 h-36 rounded-md border border-muted overflow-hidden shadow-sm">
              <Image
                src={imagePreview}
                alt="Item preview"
                fill
                className="rounded-md object-cover"
                data-ai-hint="product image"
              />
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-1.5 right-1.5 h-7 w-7 z-10 opacity-80 hover:opacity-100"
                onClick={clearImage}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Remove image</span>
              </Button>
            </div>
          )}
          <FormDescription>
            Upload an image for the item (max {MAX_ITEM_IMAGE_DIMENSION}px). Replaces any existing image.
          </FormDescription>
           <FormMessage />
        </FormItem>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea placeholder="Add any details like size, color, model, etc." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full sm:w-auto">
          {itemToEdit ? (isAdminEdit ? "Save Admin Changes" : "Save Changes") : "Add Item to Wishlist"}
        </Button>
      </form>
    </Form>
  );
}
