
"use client";

import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { WishlistItem, User } from '@/lib/types';
import {
  useWishlist, // Context hook
  // Utility functions are now imported directly from 'wishlistUtils' or their respective services
} from '@/contexts/WishlistContext';
import {
  getTotalCommittedQuantity,
  getUserCommittedQuantity,
  getAvailableQuantity,
  getReservableQuantity,
  getDirectlyPurchasableQuantity
} from '@/lib/wishlistUtils'; // Import helpers
import { Edit3, ExternalLink, Trash2, ImageIcon, ShoppingBag, Bookmark, BookmarkMinus, BookmarkCheck, RotateCcw, Tag, Gift, CheckSquare, Undo2, CopyPlus, Star, Users, CalendarDays } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import { QuantityCommitDialog } from './QuantityCommitDialog';
import { cn } from '@/lib/utils';

interface WishlistItemCardProps {
  item: WishlistItem;
  itemOwner: User;
  isOwnList: boolean;
  onEdit?: (item: WishlistItem) => void;
}

export function WishlistItemCard({ item, itemOwner, isOwnList, onEdit }: WishlistItemCardProps) {
  const {
    currentUser,
    users, // For commitment summary
    categories,
    ranks,
    reserveItem,
    unreserveItem,
    purchaseItem,
    returnItem,
    markItemAsReceived,
    unmarkItemAsReceived,
    removeItemFromCurrentUserWishlist,
    copyItemToCurrentUserWishlist,
  } = useWishlist();
  const { toast } = useToast();

  const [isCommitDialogOpen, setIsCommitDialogOpen] = useState(false);
  const [commitDialogAction, setCommitDialogAction] = useState<'reserve' | 'purchase' | 'unreserve' | 'return' | 'receive' | 'unreceive_owner' | null>(null);

  const totalReservedOverall = getTotalCommittedQuantity(item, 'reserved');
  const totalPurchasedOverall = getTotalCommittedQuantity(item, 'purchased');
  const itemDesiredQuantity = item.quantity;
  const itemReceivedQuantity = item.receivedQuantity || 0;

  const currentUserReservedQty = currentUser ? getUserCommittedQuantity(item, currentUser.id, 'reserved') : 0;
  const currentUserPurchasedQty = currentUser ? getUserCommittedQuantity(item, currentUser.id, 'purchased') : 0;

  const availableForPurchaseQty = getAvailableQuantity(item);
  const reservableForCurrentUserQty = currentUser ? getReservableQuantity(item, currentUser.id) : 0;
  const directlyPurchasableForCurrentUserQty = currentUser ? getDirectlyPurchasableQuantity(item, currentUser.id) : 0;

  const remainingForOwnerToReceive = itemDesiredQuantity - itemReceivedQuantity;

  const category = item.categoryId ? categories.find(c => c.id === item.categoryId) : null;
  const rank = item.rankId ? ranks.find(r => r.id === item.rankId) : null;

  const handleOpenCommitDialog = (action: 'reserve' | 'purchase' | 'unreserve' | 'return' | 'receive' | 'unreceive_owner') => {
    if (!currentUser) {
      toast({ title: "Login Required", description: "Please log in to perform this action.", variant: "destructive" });
      return;
    }
    setCommitDialogAction(action);
    setIsCommitDialogOpen(true);
  };

  const handleSubmitCommitDialog = async (quantity: number) => {
    if (!currentUser || !commitDialogAction) return;

    let success = false;
    let actionText = "";
    const ownerId = item.ownerUid;
    const itemName = item.name;
    const itemId = item.id;

    // Determine the specific commitment ID to modify if applicable
    // This logic is crucial for unreserve, return, and purchase-from-reservation
    const userReservedCommitmentForThisItem = (item.commitments || []).find(c => c.committedByUid === currentUser.id && c.type === 'reserved');
    const userPurchasedCommitmentForThisItem = (item.commitments || []).find(c => c.committedByUid === currentUser.id && c.type === 'purchased');
    
    let commitmentIdToModify: string | undefined = undefined;
    if (commitDialogAction === 'unreserve') commitmentIdToModify = userReservedCommitmentForThisItem?.id;
    else if (commitDialogAction === 'return') commitmentIdToModify = userPurchasedCommitmentForThisItem?.id;
    else if (commitDialogAction === 'purchase' && currentUserReservedQty > 0) commitmentIdToModify = userReservedCommitmentForThisItem?.id;


    if (commitDialogAction === 'reserve') {
      success = await reserveItem(ownerId, itemId, quantity);
      actionText = "reserved";
    } else if (commitDialogAction === 'purchase') {
      success = await purchaseItem(ownerId, itemId, quantity, commitmentIdToModify); // Pass source commitment ID if converting
      actionText = "purchased";
    } else if (commitDialogAction === 'unreserve') {
      success = await unreserveItem(ownerId, itemId, quantity, commitmentIdToModify);
      actionText = "unreserved";
    } else if (commitDialogAction === 'return') {
      success = await returnItem(ownerId, itemId, quantity, commitmentIdToModify);
      actionText = "returned";
    } else if (commitDialogAction === 'receive' && isOwnList) {
        success = await markItemAsReceived(itemId, quantity);
        actionText = "marked as received";
    } else if (commitDialogAction === 'unreceive_owner' && isOwnList) {
        success = await unmarkItemAsReceived(itemId, quantity);
        actionText = "unmarked as received";
    }

    if (success) {
      toast({
        title: `Item ${actionText}`,
        description: `${quantity} unit(s) of "${itemName}" ${actionText}.`,
      });
    } else {
       toast({
        title: "Action Failed",
        description: `Could not ${commitDialogAction} ${quantity} unit(s) of "${itemName}". Check if quantity is valid or item status changed.`,
        variant: "destructive",
      });
    }
    setIsCommitDialogOpen(false);
    setCommitDialogAction(null);
  };

  const handleDelete = async () => {
    if (!currentUser) return;
    const success = await removeItemFromCurrentUserWishlist(item.id);
    if (success) {
      toast({
        title: "Item Deleted",
        description: `"${item.name}" has been removed from your wishlist.`,
      });
    }
    // Error toast is handled by context if removeItemFromCurrentUserWishlist returns false
  };

  const handleCopyToMyList = async () => {
    if (!currentUser) return;
    if (await copyItemToCurrentUserWishlist(item)) {
      toast({
        title: "Item Copied",
        description: `"${item.name}" has been added to your wishlist.`,
      });
    }
    // Error toast handled by context if copyItemToCurrentUserWishlist returns false
  };

  const getCommitmentSummary = () => {
    if (isOwnList || !item.commitments || item.commitments.length === 0) return null;

    const committers = new Map<string, { name: string, reserved: number, purchased: number }>();
    (item.commitments || []).forEach(c => {
      const user = users.find(u => u.id === c.committedByUid);
      if (user) {
        const userName = user.id === currentUser?.id ? "You" : user.name.split(' ')[0];
        const existing = committers.get(user.id) || { name: userName, reserved: 0, purchased: 0 };
        if (c.type === 'reserved') existing.reserved += c.quantity;
        if (c.type === 'purchased') existing.purchased += c.quantity;
        committers.set(user.id, existing);
      }
    });

     if (committers.size === 0) return null;

    const summaryParts = Array.from(committers.values()).map(info => {
      let parts = [];
      if (info.purchased > 0) parts.push(`${info.purchased} purchased`);
      if (info.reserved > 0) parts.push(`${info.reserved} reserved`);
      if (parts.length === 0) return null; // Should not happen if they are in committers map
      return `${info.name} (${parts.join(', ')})`;
    }).filter(Boolean);

    if (summaryParts.length === 0) return null;

    return (
      <div className="text-xs text-muted-foreground flex items-center pt-1 flex-wrap gap-x-1">
        <Users className="h-3 w-3 mr-1 flex-shrink-0"/>
        Committed by: {summaryParts.join('; ')}
      </div>
    );
  };

  const isFullyReceivedByOwner = itemReceivedQuantity >= itemDesiredQuantity;

  // Determine which commitment ID to pass to the dialog if an existing commitment is being modified
  const userReservedCommitmentForThisItem = currentUser ? (item.commitments || []).find(c => c.committedByUid === currentUser.id && c.type === 'reserved') : undefined;
  const userPurchasedCommitmentForThisItem = currentUser ? (item.commitments || []).find(c => c.committedByUid === currentUser.id && c.type === 'purchased') : undefined;
  
  const dialogCommitmentId =
    commitDialogAction === 'unreserve' ? userReservedCommitmentForThisItem?.id :
    commitDialogAction === 'return' ? userPurchasedCommitmentForThisItem?.id :
    (commitDialogAction === 'purchase' && currentUserReservedQty > 0) ? userReservedCommitmentForThisItem?.id : undefined;


  return (
    <>
      <Card className="flex flex-col h-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg overflow-hidden" id={`item-${item.id}`}>
        <CardHeader className="pb-2">
          <div className="relative w-full h-48 bg-muted rounded-t-md flex items-center justify-center">
            {item.imageUrl ? (
              <Image
                src={item.imageUrl}
                alt={item.name}
                fill
                className="object-cover rounded-t-md"
                data-ai-hint="product image"
              />
            ) : (
              <ImageIcon className="h-16 w-16 text-muted-foreground" />
            )}
            {!isOwnList && !isFullyReceivedByOwner && (
              <div className="absolute top-2 right-2 z-10 flex flex-col items-end gap-1">
                {totalPurchasedOverall > 0 && (
                  <Badge variant="default" className="bg-accent text-accent-foreground">
                    <ShoppingBag className="mr-1 h-3 w-3" /> {totalPurchasedOverall} Purchased
                  </Badge>
                )}
                {(itemDesiredQuantity - totalPurchasedOverall - itemReceivedQuantity) > 0 && totalReservedOverall > 0 && (
                  <Badge variant="secondary" className="bg-primary text-primary-foreground">
                    <BookmarkCheck className="mr-1 h-3 w-3" /> {totalReservedOverall} Reserved
                  </Badge>
                )}
              </div>
            )}
          </div>
          <CardTitle className="mt-4 text-lg leading-tight">{item.name}</CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            At: {item.retailer} | Desired: {itemDesiredQuantity}
            {isOwnList && itemReceivedQuantity > 0 && !isFullyReceivedByOwner && (
                <span className="text-green-600 font-medium ml-2">({itemReceivedQuantity} Received)</span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-grow space-y-2 pt-0">
          <div className="flex justify-between items-center">
            <p className="text-xl font-semibold text-primary">${item.price.toFixed(2)}</p>
            {!isOwnList && !isFullyReceivedByOwner && (
                 <Badge variant="outline">
                   {(itemDesiredQuantity - totalPurchasedOverall - itemReceivedQuantity) > 0 ? `${(itemDesiredQuantity - totalPurchasedOverall - itemReceivedQuantity)} still needed` : "All accounted for"}
                 </Badge>
            )}
             {isFullyReceivedByOwner && isOwnList && (
                <Badge variant="default" className="bg-green-600 text-white"><CheckSquare className="mr-1 h-3 w-3"/>Fully Received</Badge>
            )}
          </div>
          {category && <p className="text-xs text-muted-foreground flex items-center"><Tag className="h-3 w-3 mr-1 text-muted-foreground/70"/> Category: {category.title}</p>}
          {rank && <p className="text-sm text-muted-foreground flex items-center">{rank.title}</p>}
          {item.description && <p className="text-sm text-foreground/80 line-clamp-2 pt-1">{item.description}</p>}
          {item.dateAdded && (
            <p className="text-xs text-muted-foreground mt-1 flex items-center">
              <CalendarDays className="h-3.5 w-3.5 mr-1.5 text-muted-foreground/70" />
              Added: {new Date(item.dateAdded).toLocaleDateString()}
            </p>
          )}
          {!isOwnList && getCommitmentSummary()}
        </CardContent>
        <CardFooter className={cn(
            "flex items-center gap-2 pt-2 pb-4 px-4 justify-between flex-wrap"
          )}>
          <div className={cn("mr-auto", isOwnList && "w-full flex justify-start")}>
            {item.url && (
              <Button variant="outline" size="sm" asChild>
                  <Link href={item.url} target="_blank" rel="noopener noreferrer">
                      <ExternalLink /> View Online
                  </Link>
              </Button>
            )}
          </div>

          <div className={cn(
              "flex flex-wrap items-center gap-2",
               isOwnList ? "justify-start" : "justify-end"
            )}>
              {isOwnList ? (
                <>
                  {itemReceivedQuantity > 0 && (
                    <Button variant="outline" size="sm" onClick={() => handleOpenCommitDialog('unreceive_owner')}>
                        <Undo2 /> Unmark Received
                    </Button>
                  )}
                  {!isFullyReceivedByOwner && remainingForOwnerToReceive > 0 && (
                    <Button variant="outline" size="sm" onClick={() => handleOpenCommitDialog('receive')}>
                        <Gift /> Mark Received
                    </Button>
                  )}
                  <Button variant="outline" size="sm" onClick={() => onEdit?.(item)}>
                    <Edit3 /> Edit
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 /> Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete "{item.name}" from your wishlist.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </>
              ) : (
                <>
                  {currentUser && (
                    <>
                      <Button size="sm" variant="outline" onClick={handleCopyToMyList}>
                        <CopyPlus /> Add to My List
                      </Button>
                      {currentUserPurchasedQty > 0 && (
                        <Button size="sm" onClick={() => handleOpenCommitDialog('return')} variant="outline">
                          <RotateCcw /> Return
                        </Button>
                      )}
                      {currentUserReservedQty > 0 && (
                        <Button size="sm" onClick={() => handleOpenCommitDialog('unreserve')} variant="outline">
                          <BookmarkMinus /> Unreserve
                        </Button>
                      )}
                      {/* Reserve button: if user can reserve and hasn't already reserved or purchased */}
                      {reservableForCurrentUserQty > 0 && currentUserReservedQty === 0 && currentUserPurchasedQty === 0 && !isFullyReceivedByOwner && (
                        <Button size="sm" onClick={() => handleOpenCommitDialog('reserve')} variant="default" disabled={reservableForCurrentUserQty <=0}>
                          <Bookmark /> Reserve
                        </Button>
                      )}
                      {/* Purchase button to convert existing reservation */}
                      {currentUserReservedQty > 0 && (
                        <Button size="sm" onClick={() => handleOpenCommitDialog('purchase')} variant="default" disabled={Math.min(currentUserReservedQty, availableForPurchaseQty) <= 0 || isFullyReceivedByOwner}>
                          <ShoppingBag /> Purchased
                        </Button>
                      )}
                      {/* Purchase button for new direct purchase (no prior reservation by current user) */}
                      {directlyPurchasableForCurrentUserQty > 0 && currentUserReservedQty === 0 && currentUserPurchasedQty === 0 && !isFullyReceivedByOwner && (
                        <Button size="sm" onClick={() => handleOpenCommitDialog('purchase')} variant="default" disabled={directlyPurchasableForCurrentUserQty <=0}>
                          <ShoppingBag /> Purchased
                        </Button>
                      )}

                      {!reservableForCurrentUserQty && !directlyPurchasableForCurrentUserQty && currentUserReservedQty === 0 && currentUserPurchasedQty === 0 && !isFullyReceivedByOwner && (
                          <p className="text-xs text-muted-foreground text-center">Fully Committed</p>
                      )}
                      {isFullyReceivedByOwner && <p className="text-xs text-green-600">Fully Received by Owner</p>}
                    </>
                  )}
                </>
              )}
            </div>
        </CardFooter>
      </Card>
      {commitDialogAction && currentUser && (
         <QuantityCommitDialog
          isOpen={isCommitDialogOpen}
          onOpenChange={setIsCommitDialogOpen}
          item={item}
          actionType={commitDialogAction}
          commitmentId={dialogCommitmentId}
          maxQuantity={
            commitDialogAction === 'reserve' ? reservableForCurrentUserQty :
            commitDialogAction === 'purchase' ? (currentUserReservedQty > 0 ? Math.min(currentUserReservedQty, availableForPurchaseQty) : directlyPurchasableForCurrentUserQty) :
            commitDialogAction === 'unreserve' ? currentUserReservedQty :
            commitDialogAction === 'return' ? currentUserPurchasedQty :
            commitDialogAction === 'receive' ? remainingForOwnerToReceive :
            commitDialogAction === 'unreceive_owner' ? itemReceivedQuantity : 0
          }
          currentCommittedQuantity={ 
            commitDialogAction === 'unreserve' ? currentUserReservedQty :
            commitDialogAction === 'return' ? currentUserPurchasedQty :
            (commitDialogAction === 'purchase' && currentUserReservedQty > 0) ? currentUserReservedQty :
            (commitDialogAction === 'receive' ? itemReceivedQuantity :
            (commitDialogAction === 'unreceive_owner' ? itemReceivedQuantity : undefined))
          }
          onSubmit={handleSubmitCommitDialog}
        />
      )}
    </>
  );
}
