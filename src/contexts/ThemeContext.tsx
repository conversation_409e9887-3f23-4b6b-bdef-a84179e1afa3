
"use client";

import type { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { themeStorage } from '@/lib/secureStorage';

export type Theme = 'light' | 'dark' | 'high-contrast' | 'christmas' | 'fourth-of-july' | 'easter';
interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  isHighContrast: () => boolean;
  isChristmas: () => boolean;
  isFourthOfJuly: () => boolean;
  isEaster: () => boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = 'giftlink-theme';

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      const storedTheme = themeStorage.get() as Theme | null;
      if (storedTheme) {
        return storedTheme;
      }
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
      }
    }
    return 'light'; // Default theme
  });

  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    if (typeof window !== 'undefined') {
      themeStorage.set(newTheme);
    }
  }, []);

  useEffect(() => {
    const root = window.document.documentElement;

    root.classList.remove('dark', 'high-contrast', 'christmas', 'fourth-of-july', 'easter');

    if (theme === 'dark') {
      root.classList.add('dark');
    } else if (theme === 'high-contrast') {
      root.classList.add('high-contrast');
    } else if (theme === 'christmas') {
      root.classList.add('christmas');
    } else if (theme === 'fourth-of-july') {
      root.classList.add('fourth-of-july');
    } else if (theme === 'easter') {
      root.classList.add('easter');
    }
    // For 'light' theme, no specific class is added; it relies on the :root defaults.
  }, [theme]);

  // Effect to listen to system preference changes if no theme is stored
  useEffect(() => {
    if (typeof window !== 'undefined' && !themeStorage.get()) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        if (!themeStorage.get()) { // Check again in case user set a theme manually
          setThemeState(mediaQuery.matches ? 'dark' : 'light');
        }
      };
      mediaQuery.addEventListener('change', handleChange);
      // Set initial theme based on system preference if no stored theme
      if (!themeStorage.get()) {
        setThemeState(mediaQuery.matches ? 'dark' : 'light');
      }
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);

  const isHighContrast = useCallback(() => theme === 'high-contrast', [theme]);
  const isChristmas = useCallback(() => theme === 'christmas', [theme]);
  const isFourthOfJuly = useCallback(() => theme === 'fourth-of-july', [theme]);
  const isEaster = useCallback(() => theme === 'easter', [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, isHighContrast, isChristmas, isFourthOfJuly, isEaster }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
