"use client";

import type { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import type { User as AppUser, WishlistItem, Wishlist, Commitment, Message, Event, UpcomingEventDisplayInfo, Group, Category, Rank } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';
import { addMonths, format, getYear, parse, isWithinInterval, setYear, startOfDay, isValid, isDate, differenceInDays } from 'date-fns';
import { type User as FirebaseUser, signInWithEmailAndPassword, signOut, onAuthStateChanged, updatePassword as firebaseUpdatePassword } from 'firebase/auth';
import {
  doc, getDoc, collection, getDocs, setDoc, addDoc, updateDoc, deleteDoc,
  writeBatch, query, where, orderBy, serverTimestamp, Timestamp, deleteField,
  limit, type FieldValue, type QuerySnapshot, type DocumentData,
  documentId,
} from 'firebase/firestore';
import { useToast } from '@/hooks/use-toast';
import { auth, db } from '@/lib/firebase';
import { addAuthUserAndProfile } from '@/services/wishlistAuthService';

// --- MODULE-LEVEL HELPER FUNCTIONS ---
const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) return error.message;
  return String(error);
};

// Helper function to remove undefined values from objects recursively
const removeUndefinedValues = (obj: any): any => {
  if (obj === null || obj === undefined) return null;
  if (Array.isArray(obj)) {
    return obj.map(removeUndefinedValues).filter(item => item !== undefined);
  }
  if (typeof obj === 'object') {
    const cleaned: any = {};
    Object.keys(obj).forEach(key => {
      const value = removeUndefinedValues(obj[key]);
      if (value !== undefined) {
        cleaned[key] = value;
      }
    });
    return cleaned;
  }
  return obj;
};

import { generateSecureId } from '@/lib/validation';

const generateUniqueId = (): string => {
  return generateSecureId(20);
};

const formatTimestamp = (timestampInput: any): string | undefined => {
    if (!timestampInput) return undefined;
    let date: Date | null = null;
    if (timestampInput instanceof Timestamp) {
        date = timestampInput.toDate();
    } else if (typeof timestampInput === 'string' || typeof timestampInput === 'number') {
        try { const parsed = new Date(timestampInput); if (isValid(parsed)) date = parsed; } catch (e) { /* ignore */ }
    } else if (timestampInput && typeof timestampInput.seconds === 'number' && typeof timestampInput.nanoseconds === 'number') {
        try { const parsed = new Date(timestampInput.seconds * 1000 + timestampInput.nanoseconds / 1000000); if (isValid(parsed)) date = parsed; } catch (e) { /* ignore */ }
    } else if (isDate(timestampInput) && isValid(timestampInput)) {
        date = timestampInput;
    }
    return date && isValid(date) ? date.toISOString() : undefined;
};

export const getTotalCommittedQuantity = (item: WishlistItem, type?: 'reserved' | 'purchased'): number => {
    if (!item || !Array.isArray(item.commitments)) return 0;
    return item.commitments.reduce((sum, c) => (!type || c.type === type) ? sum + (c.quantity || 0) : sum, 0);
};
export const getUserCommittedQuantity = (item: WishlistItem, userId: string, type?: 'reserved' | 'purchased'): number => {
    if (!item || !Array.isArray(item.commitments) || !userId) return 0;
    return item.commitments.reduce((sum, c) => (c.committedByUid === userId && (!type || c.type === type)) ? sum + (c.quantity || 0) : sum, 0);
};
export const getAvailableQuantity = (item: WishlistItem): number => {
    if (!item) return 0;
    const totalDesired = item.quantity || 0;
    const totalPurchased = getTotalCommittedQuantity(item, 'purchased');
    const totalReceivedByOwner = item.receivedQuantity || 0;
    return Math.max(0, totalDesired - totalPurchased - totalReceivedByOwner);
};
export const getReservableQuantity = (item: WishlistItem, currentUserId: string | null): number => {
    if (!item || !currentUserId) return 0;
    const totalDesired = item.quantity || 0;
    const totalPurchasedByAnyone = getTotalCommittedQuantity(item, 'purchased');
    const totalReceivedByOwner = item.receivedQuantity || 0;
    const commitments = Array.isArray(item.commitments) ? item.commitments : [];
    const totalReservedByOthers = commitments.filter(c => c.type === 'reserved' && c.committedByUid !== currentUserId).reduce((sum, c) => sum + (c.quantity || 0), 0);
    const neededAndNotPurchasedOrReceived = Math.max(0, totalDesired - totalPurchasedByAnyone - totalReceivedByOwner);
    return Math.max(0, neededAndNotPurchasedOrReceived - totalReservedByOthers);
};
export const getDirectlyPurchasableQuantity = (item: WishlistItem, currentUserId: string | null): number => {
    if (!item || !currentUserId) return 0; 
    const totalDesired = item.quantity || 0;
    const totalPurchasedOverall = getTotalCommittedQuantity(item, 'purchased');
    const totalReceivedByOwner = item.receivedQuantity || 0;

    const stillNeededByOwner = Math.max(0, totalDesired - totalPurchasedOverall - totalReceivedByOwner);
    if (stillNeededByOwner === 0) return 0;

    const commitments = Array.isArray(item.commitments) ? item.commitments : [];
    const reservedByOthers = commitments
      .filter(c => c.type === 'reserved' && c.committedByUid !== currentUserId)
      .reduce((sum, c) => sum + (c.quantity || 0), 0);

    return Math.max(0, stillNeededByOwner - reservedByOthers);
};
// --- END MODULE-LEVEL HELPER FUNCTIONS ---


interface WishlistContextType {
  currentUser: AppUser | null;
  firebaseUser: FirebaseUser | null;
  authLoading: boolean;
  users: AppUser[];
  wishlists: Wishlist[]; // Shells, no items array
  allItems: WishlistItem[]; // All items from 'items' collection
  messages: Message[];
  events: Event[];
  groupsData: Group[];
  categories: Category[];
  ranks: Rank[];
  upcomingEventsDisplayMonths: number;

  getWishlistByUserId: (userId: string) => Promise<Wishlist | undefined>;
  getItemsForUser: (userId: string) => WishlistItem[];
  getItemById: (itemId: string) => WishlistItem | undefined;

  addItemToCurrentUserWishlist: (itemDetails: Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  updateItemInCurrentUserWishlist: (itemId: string, itemDetails: Partial<Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  updateAnyWishlistItem: (itemId: string, itemDetails: Partial<Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  deleteAnyWishlistItemByAdmin: (itemId: string, adminReason: string) => Promise<boolean>;
  deleteAllWishlistItemsFromAllUsers: () => Promise<boolean>;
  removeItemFromCurrentUserWishlist: (itemId: string) => Promise<boolean>;
  copyItemToCurrentUserWishlist: (itemToCopy: WishlistItem) => Promise<boolean>;

  reserveItem: (itemOwnerUserId: string, itemId: string, quantity: number) => Promise<boolean>;
  unreserveItem: (itemOwnerUserId: string, itemId: string, quantityToUnreserve: number, commitmentIdToModify?: string) => Promise<boolean>;
  purchaseItem: (itemOwnerUserId: string, itemId: string, quantity: number, sourceCommitmentId?: string) => Promise<boolean>;
  returnItem: (itemOwnerUserId: string, itemId: string, quantityToReturn: number, commitmentIdToModify?: string) => Promise<boolean>;

  markItemAsReceived: (itemId: string, quantityReceived: number) => Promise<boolean>;
  unmarkItemAsReceived: (itemId: string, quantityToUnreceive: number) => Promise<boolean>;

  updateUserEmail: (newEmail: string) => Promise<boolean>;
  changeUserPassword: (newPassword: string) => Promise<boolean>;
  updateUserAllowEmailsPreference: (allowEmails: boolean) => Promise<boolean>;
  updateUserDisplayEmailPreference: (displayEmail: boolean) => Promise<boolean>;
  updateUserAvatar: (avatarUrl: string) => Promise<boolean>;

  getUnreadMessagesCount: (userId: string) => number;
  getMessagesForUser: (userId: string) => Message[];
  getAllMessages: () => Message[];
  deleteNotificationById: (messageId: string) => Promise<boolean>;
  deleteAllNotificationsForUser: (userId: string) => Promise<boolean>;
  deleteAllNotificationsAndNotifyUsers: () => Promise<boolean>;
  markMessagesAsRead: (userId: string, messageIds?: string[]) => Promise<void>;

  getUpcomingEventsForUser: (userId: string) => UpcomingEventDisplayInfo[];
  addUserEvent: (eventData: Omit<Event, 'id' | 'type' | 'createdAt' | 'updatedAt' | 'createdByUserId'>) => Promise<boolean>;
  addAdminEvent: (eventData: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  updateAdminEvent: (eventId: string, eventData: Partial<Omit<Event, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  deleteAdminEvent: (eventId: string) => Promise<boolean>;

  addCategory: (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  updateCategory: (categoryId: string, categoryData: Partial<Omit<Category, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  deleteCategory: (categoryId: string) => Promise<{ success: boolean; message?: string }>;

  addGroup: (groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  updateGroup: (groupId: string, groupData: Partial<Omit<Group, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  deleteGroup: (groupId: string) => Promise<{ success: boolean; message?: string }>;

  addRank: (rankData: Omit<Rank, 'id' | 'createdAt' | 'updatedAt'>) => Promise<boolean>;
  updateRank: (rankId: string, rankData: Partial<Omit<Rank, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  deleteRank: (rankId: string) => Promise<{ success: boolean; message?: string }>;

  addUser: (userData: Omit<AppUser, 'id' | 'avatarUrl' | 'createdAt' | 'updatedAt'> & { password?: string }) => Promise<{success: boolean; newUserId?: string}>;
  updateUser: (userId: string, userData: Partial<Omit<AppUser, 'id' | 'avatarUrl' | 'password' | 'createdAt' | 'updatedAt'>>) => Promise<boolean>;
  deleteUser: (userIdToDelete: string) => Promise<{ success: boolean; message?: string }>;

  signInUser: (email: string, pass: string) => Promise<boolean>;
  signOutUser: () => Promise<void>;
  refreshAllDataFromFirestore: () => Promise<void>;
  canViewWishlist: (targetUserId: string) => boolean;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export const WishlistProvider = ({ children }: { children: ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<AppUser | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [authLoading, setAuthLoading] = useState<boolean>(true);

  const [users, setUsers] = useState<AppUser[]>([]);
  const [groupsData, setGroupsData] = useState<Group[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [ranks, setRanks] = useState<Rank[]>([]);
  const [wishlists, setWishlists] = useState<Wishlist[]>([]); 
  const [allItems, setAllItems] = useState<WishlistItem[]>([]); 
  const [messages, setMessages] = useState<Message[]>([]);
  const [lastEmailSentTimestamps, setLastEmailSentTimestamps] = useState<Record<string, Record<string, number>>>({});
  const [upcomingEventsDisplayMonths] = useState<number>(3);

  const { toast } = useToast();

  const addMessageToList_internal = async (messageData: Omit<Message, 'id' | 'timestamp' | 'isRead' | 'updatedAt'>): Promise<string | null> => {
    if (!db) { console.error("Firestore DB not initialized. Cannot add message."); return null; }
    const dataForFirestore: Partial<Omit<Message, 'id'>> & { timestamp: FieldValue; updatedAt: FieldValue; isRead: boolean } = {
      recipientUid: messageData.recipientUid, eventType: messageData.eventType, content: messageData.content,
      isRead: false, timestamp: serverTimestamp(), updatedAt: serverTimestamp(),
    };
    if (messageData.actingUid !== undefined) dataForFirestore.actingUid = messageData.actingUid;
    if (messageData.actingUserName !== undefined) dataForFirestore.actingUserName = messageData.actingUserName;
    if (messageData.itemInfo !== undefined) dataForFirestore.itemInfo = messageData.itemInfo;
    if (messageData.commitmentInfo !== undefined) dataForFirestore.commitmentInfo = messageData.commitmentInfo;
    if (messageData.adminReason !== undefined) dataForFirestore.adminReason = messageData.adminReason;
    if (messageData.changes !== undefined && messageData.changes.length > 0) {
      // Filter out any changes that might have undefined values
      const cleanChanges = messageData.changes.filter(change => 
        change.field !== undefined && 
        (change.oldValue !== undefined || change.newValue !== undefined)
      );
      if (cleanChanges.length > 0) {
        dataForFirestore.changes = cleanChanges;
      }
    }
    try {
      // Clean the data to remove any undefined values before sending to Firestore
      const cleanedData = removeUndefinedValues(dataForFirestore);
      const docRef = await addDoc(collection(db, "messages"), cleanedData);
      const newMessageForClientState: Message = {
        id: docRef.id, ...messageData, timestamp: new Date().toISOString(),
        updatedAt: new Date().toISOString(), isRead: false,
      };
      setMessages(prev => [newMessageForClientState, ...prev].sort((a, b) => {
        const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        return timeB - timeA;
      }));
      return docRef.id;
    } catch (error) {
      console.error("Error adding message to Firestore: ", error);
      toast({title: "Notification Error", description: "Could not create an in-app notification.", variant: "destructive"})
      return null;
    }
  };

  const _generateAndDispatchNotifications_internal = async (
    eventType: Message['eventType'] | 'user_deleted_commitment_impacted',
    actingUser: AppUser | { id: 'system', name: 'System Admin' },
    itemInfo: Message['itemInfo'],
    itemOwnerUserId: string,
    originalItemForCommitmentsCheck?: WishlistItem, 
    deletedUserName?: string,
    adminReason?: string,
  ) => {
    const itemOwnerProfile = users.find(u => u.id === itemOwnerUserId);
    const actingUserNameDisplay = actingUser.name || (actingUser.id === 'system' ? 'System Admin' : 'A user');
    const itemNameDisplay = itemInfo?.name || originalItemForCommitmentsCheck?.name || 'Unknown Item';
    const itemOwnerNameDisplay = itemOwnerProfile?.name || deletedUserName || 'Unknown User';

    if (['item_added', 'item_updated', 'item_deleted_by_owner', 'item_updated_by_admin'].includes(eventType) && itemOwnerProfile) {
      const relevantUsers = users.filter(u =>
        u.id !== actingUser.id && u.id !== itemOwnerUserId && u.isApproved &&
        (itemOwnerProfile.groups || []).some(gId => (u.groups || []).includes(gId))
      );
      let baseMessageContent = "";
      switch (eventType) {
        case 'item_added': baseMessageContent = `${actingUserNameDisplay} added "${itemNameDisplay}" to ${itemOwnerNameDisplay}'s wishlist.`; break;
        case 'item_updated': baseMessageContent = `${actingUserNameDisplay} updated "${itemInfo?.oldName || itemNameDisplay}" (now "${itemNameDisplay}") on ${itemOwnerNameDisplay}'s wishlist.`; break;
        case 'item_deleted_by_owner': baseMessageContent = `${actingUserNameDisplay} deleted "${itemNameDisplay}" from their wishlist.`; break;
        case 'item_updated_by_admin': baseMessageContent = `An administrator (${actingUserNameDisplay}) updated ${itemOwnerNameDisplay}'s wishlist item: "${itemNameDisplay}".`; break;
      }
      for (const recipient of relevantUsers) {
        await addMessageToList_internal({
          recipientUid: recipient.id, actingUid: actingUser.id, actingUserName: actingUser.name,
          eventType: eventType as Message['eventType'],
          itemInfo: { name: itemNameDisplay, ownerName: itemOwnerNameDisplay, id: itemInfo?.id || originalItemForCommitmentsCheck?.id },
          content: baseMessageContent,
          changes: eventType === 'item_updated_by_admin' ? itemInfo?.changes : undefined,
        });
      }
    }

    if (eventType === 'item_deleted_by_admin' && itemOwnerProfile && actingUser.id !== itemOwnerUserId) {
        await addMessageToList_internal({
            recipientUid: itemOwnerUserId, actingUid: actingUser.id, actingUserName: actingUser.name,
            eventType: 'item_deleted_by_admin',
            itemInfo: { name: itemNameDisplay, ownerName: itemOwnerNameDisplay, id: itemInfo?.id || originalItemForCommitmentsCheck?.id },
            content: `An administrator (${actingUserNameDisplay}) deleted your item "${itemNameDisplay}". Reason: ${adminReason || 'No reason provided.'}`,
            adminReason: adminReason
        });
    }
    if (eventType === 'item_updated_by_admin' && itemOwnerProfile && actingUser.id !== itemOwnerUserId) {
        await addMessageToList_internal({
            recipientUid: itemOwnerUserId, actingUid: actingUser.id, actingUserName: actingUser.name,
            eventType: 'item_updated_by_admin',
            itemInfo: { name: itemNameDisplay, ownerName: itemOwnerNameDisplay, id: itemInfo?.id || originalItemForCommitmentsCheck?.id, changes: itemInfo?.changes },
            content: `An administrator (${actingUserNameDisplay}) updated your item "${itemNameDisplay}". Please review the changes.`,
            changes: itemInfo?.changes
        });
    }

    const commitmentsToNotify = originalItemForCommitmentsCheck?.commitments || [];
    if (eventType === 'item_deleted_by_owner' || eventType === 'item_deleted_by_admin' || eventType === 'user_deleted_commitment_impacted') {
      for (const commitment of commitmentsToNotify) {
        const committerProfile = users.find(u => u.id === commitment.committedByUid);
        if (committerProfile && !(eventType === 'user_deleted_commitment_impacted' && committerProfile.id === actingUser.id)) {
          let commitmentImpactMessage = "";
          const commitmentTypeDisplay = commitment.type === 'reserved' ? 'reservation' : 'purchase';
          const originalItemName = originalItemForCommitmentsCheck?.name || 'Unknown Item';
          const effectiveOwnerName = eventType === 'user_deleted_commitment_impacted' ? (deletedUserName || 'A user') : (itemOwnerProfile?.name || itemOwnerNameDisplay);

          if (eventType === 'item_deleted_by_owner') {
            commitmentImpactMessage = `The item "${originalItemName}" (for ${effectiveOwnerName}), for which you had a ${commitmentTypeDisplay} of ${commitment.quantity}, has been deleted by the owner.`;
          } else if (eventType === 'item_deleted_by_admin') {
            commitmentImpactMessage = `The item "${originalItemName}" (for ${effectiveOwnerName}), for which you had a ${commitmentTypeDisplay} of ${commitment.quantity}, has been deleted by an administrator. Reason: ${adminReason || 'No reason provided.'}`;
          } else if (eventType === 'user_deleted_commitment_impacted') {
            commitmentImpactMessage = `User '${effectiveOwnerName}' and their wishlist have been removed. Your ${commitmentTypeDisplay} for their item '${originalItemName}' (${commitment.quantity}) is no longer applicable.`;
          }

          if (commitmentImpactMessage) {
            await addMessageToList_internal({
              recipientUid: committerProfile.id, actingUid: actingUser.id, actingUserName: actingUser.name,
              eventType: 'item_commitment_impacted_by_deletion',
              itemInfo: { name: originalItemName, ownerName: effectiveOwnerName, id: originalItemForCommitmentsCheck?.id },
              commitmentInfo: { type: commitment.type, quantity: commitment.quantity },
              content: commitmentImpactMessage,
              adminReason: eventType === 'item_deleted_by_admin' ? adminReason : undefined,
            });
          }
        }
      }
    }
  };

  const fetchAllData_internal = async (currentFbUser?: FirebaseUser | null, currentAppUser?: AppUser | null) => {
    if (!db) { return; }

    if (!currentFbUser || !currentAppUser) {
      setUsers([]); setGroupsData([]); setEvents([]); setWishlists([]); setAllItems([]); setMessages([]);
      setCategories([]); setRanks([]); // Clear categories and ranks when not authenticated
      return;
    }

    // Fetch categories and ranks only for authenticated users
    try {
      const categoriesSnap = await getDocs(query(collection(db, "categories"), orderBy("title")));
      setCategories(categoriesSnap.docs.map(d => ({ ...d.data(), id: d.id, createdAt: formatTimestamp(d.data().createdAt), updatedAt: formatTimestamp(d.data().updatedAt) } as Category)));
    } catch (error) { console.error("Error fetching categories:", error); setCategories([]); toast({ title: "Data Load Error", description: `Could not load categories. ${getErrorMessage(error)}`, variant: "destructive" }); }

    try {
      const ranksSnap = await getDocs(query(collection(db, "ranks"), orderBy("order")));
      setRanks(ranksSnap.docs.map(d => ({ ...d.data(), id: d.id, createdAt: formatTimestamp(d.data().createdAt), updatedAt: formatTimestamp(d.data().updatedAt) } as Rank)));
    } catch (error) { console.error("Error fetching ranks:", error); setRanks([]); toast({ title: "Data Load Error", description: `Could not load ranks. ${getErrorMessage(error)}`, variant: "destructive" }); }

    try {
      let fetchedUsers: AppUser[] = [];
      let fetchedWishlistShells: Wishlist[] = [];
      let fetchedItems: WishlistItem[] = [];

      if (currentAppUser.isAdmin) {
        const usersSnap = await getDocs(query(collection(db, "users"), orderBy("name")));
        fetchedUsers = usersSnap.docs.map(d => ({ ...d.data(), id: d.id, createdAt: formatTimestamp(d.data().createdAt), updatedAt: formatTimestamp(d.data().updatedAt) } as AppUser));
        
        const groupsSnap = await getDocs(query(collection(db, "groups"), orderBy("name")));
        setGroupsData(groupsSnap.docs.map(d => ({ ...d.data() as Omit<Group, 'id'>, id: d.id, createdAt: formatTimestamp(d.data().createdAt), updatedAt: formatTimestamp(d.data().updatedAt) } as Group)));
        
        const eventsSnap = await getDocs(query(collection(db, "events"), orderBy("date", "asc")));
        setEvents(eventsSnap.docs.map(d => ({ ...d.data(), id: d.id, date: String(d.data().date), createdAt: formatTimestamp(d.data().createdAt), updatedAt: formatTimestamp(d.data().updatedAt) } as Event)));
        
        const allWishlistShellsSnap = await getDocs(collection(db, "wishlists"));
        fetchedWishlistShells = allWishlistShellsSnap.docs.map(wishlistDoc => {
            const data = wishlistDoc.data();
            return {
              id: wishlistDoc.id, userId: data.userId || wishlistDoc.id, // Ensure userId is populated
              createdAt: formatTimestamp(data.createdAt), updatedAt: formatTimestamp(data.updatedAt),
            } as Wishlist;
          });

        const allItemsSnap = await getDocs(collection(db, "items"));
        fetchedItems = allItemsSnap.docs.map(itemDoc => {
            const data = itemDoc.data();
            return {
                ...data, id: itemDoc.id,
                dateAdded: formatTimestamp(data.dateAdded),
                commitments: Array.isArray(data.commitments) ? data.commitments.map((c: any) => ({...c, id: c.id || c.commitmentId || uuidv4(), createdAt: formatTimestamp(c.createdAt)})) : [],
                receivedQuantity: data.receivedQuantity || 0,
                createdAt: formatTimestamp(data.createdAt), updatedAt: formatTimestamp(data.updatedAt),
            } as WishlistItem;
        });

        const messagesCol = collection(db, "messages");
        const messagesQuery = query(messagesCol, orderBy("timestamp", "desc"), limit(200));
        const messagesSnapshot = await getDocs(messagesQuery);
        setMessages(messagesSnapshot.docs.map(d => ({ ...d.data(), id: d.id, timestamp: formatTimestamp(d.data().timestamp), updatedAt: formatTimestamp(d.data().updatedAt) } as Message)));

      } else { // Non-admin user
        fetchedUsers.push(currentAppUser);
        const otherUsersQuery = query(collection(db, "users"), where("isApproved", "==", true));
        const otherUsersSnap = await getDocs(otherUsersQuery);
        otherUsersSnap.forEach(userDoc => {
            if (userDoc.id !== currentFbUser.uid) {
              fetchedUsers.push({ ...userDoc.data(), id: userDoc.id, createdAt: formatTimestamp(userDoc.data().createdAt), updatedAt: formatTimestamp(userDoc.data().updatedAt) } as AppUser);
            }
          });
        
        // CRITICAL: Filter to only users the current user can actually view (shares groups with)
        // Firestore security rules are AUTHORIZATION, not FILTERS. If we query for documents
        // the user isn't authorized to read, the entire query will FAIL, not just filter out
        // unauthorized documents. We must pre-filter to only authorized users.
        const currentUserGroups = currentAppUser.groups || [];
        const accessibleUsers = fetchedUsers.filter(user => {
          if (user.id === currentAppUser.id) return true; // Always include self
          if (!user.isApproved) return false; // Only approved users
          const userGroups = user.groups || [];
          return currentUserGroups.some(g => userGroups.includes(g)); // Must share at least one group
        });
        
        // Update fetchedUsers to only include accessible users
        fetchedUsers = accessibleUsers;
        
        const accessibleUserIds = accessibleUsers.map(u => u.id);
        if (accessibleUserIds.length > 0) {
            // CRITICAL FIX: Fetch wishlist shells individually to respect Firestore security rules
            // Batch queries with "in" operator fail when any document in the result set
            // would be blocked by security rules. The wishlist security rule checks document ID,
            // not a userId field, so we must fetch each user's wishlist individually.
            console.log(`[DEBUG] Fetching wishlists for ${accessibleUserIds.length} users individually`);

            for (const userId of accessibleUserIds) {
              try {
                console.log(`[DEBUG] Attempting to fetch wishlist for user ${userId}`);
                const wishlistDocRef = doc(db, "wishlists", userId);
                const wishlistDoc = await getDoc(wishlistDocRef);

                if (wishlistDoc.exists()) {
                  console.log(`[DEBUG] Successfully fetched wishlist for user ${userId}`);
                  const data = wishlistDoc.data();
                  fetchedWishlistShells.push({
                    id: wishlistDoc.id,
                    userId: userId, // Use the document ID as userId since that's how the security rules work
                    createdAt: formatTimestamp(data.createdAt),
                    updatedAt: formatTimestamp(data.updatedAt),
                  } as Wishlist);
                } else {
                  console.log(`[DEBUG] No wishlist document found for user ${userId}`);
                }
              } catch (error) {
                // Individual user wishlist fetch failed - likely due to security rules
                // This is expected for users we don't share groups with
                console.log(`Could not fetch wishlist for user ${userId}:`, getErrorMessage(error));
              }
            }

            // CRITICAL FIX: Fetch items individually to respect Firestore security rules
            // Batch queries with "in" operator fail when any document in the result set
            // would be blocked by security rules. We must query each user's items individually.
            console.log(`[DEBUG] Fetching items for ${accessibleUserIds.length} users individually`);

            for (const userId of accessibleUserIds) {
              try {
                const userItemsQuery = query(collection(db, "items"), where("ownerUid", "==", userId));
                const userItemsSnap = await getDocs(userItemsQuery);
                const userItems = userItemsSnap.docs.map(itemDoc => {
                  const data = itemDoc.data();
                  return {
                    ...data, id: itemDoc.id,
                    dateAdded: formatTimestamp(data.dateAdded),
                    commitments: Array.isArray(data.commitments) ? data.commitments.map((c: any) => ({...c, id: c.id || c.commitmentId || uuidv4(), createdAt: formatTimestamp(c.createdAt)})) : [],
                    receivedQuantity: data.receivedQuantity || 0,
                    createdAt: formatTimestamp(data.createdAt), updatedAt: formatTimestamp(data.updatedAt),
                  } as WishlistItem;
                });
                fetchedItems.push(...userItems);
              } catch (error) {
                // Individual user items fetch failed - likely due to security rules
                // This is expected for users we don't share groups with
                console.log(`Could not fetch items for user ${userId}:`, getErrorMessage(error));
              }
            }
        }
        
        try {
            const messagesCol = collection(db, "messages");
            const userMessagesQuery = query(messagesCol, where("recipientUid", "==", currentFbUser.uid), orderBy("timestamp", "desc"), limit(50));
            const userMessagesSnapshot = await getDocs(userMessagesQuery);
            setMessages(userMessagesSnapshot.docs.map(d => ({ ...d.data(), id: d.id, timestamp: formatTimestamp(d.data().timestamp), updatedAt: formatTimestamp(d.data().updatedAt) } as Message)));
        } catch (error) { console.error("Error fetching user-specific messages:", error); setMessages([]); toast({ title: "Notification Load Error", description: `Could not load your notifications. ${getErrorMessage(error)}`, variant: "destructive" }); }
        
        if (currentAppUser.groups && currentAppUser.groups.length > 0) {
          try {
            const groupsQuery = query(collection(db, "groups"), where(documentId(), "in", currentAppUser.groups.slice(0,30)));
            const groupsSnap = await getDocs(groupsQuery);
            setGroupsData(groupsSnap.docs.map(d => ({ ...d.data() as Omit<Group, 'id'>, id: d.id, createdAt: formatTimestamp(d.data().createdAt), updatedAt: formatTimestamp(d.data().updatedAt) } as Group)));
          } catch (err) { console.error("Error fetching groupsQuery", err); setGroupsData([]); toast({ title: "Group Data Error", description: "Could not load your group details.", variant: "destructive"}); }
        } else { setGroupsData([]); }
        
        const relevantEvents: Event[] = [];
        try {
            const systemEventsQuery = query(collection(db, "events"), where("type", "==", "system"));
            const systemEventsSnap = await getDocs(systemEventsQuery);
            systemEventsSnap.forEach(doc => relevantEvents.push({ ...doc.data(), id: doc.id, date: String(doc.data().date), createdAt: formatTimestamp(doc.data().createdAt), updatedAt: formatTimestamp(doc.data().updatedAt) } as Event));
        } catch(err) { console.error("Error fetching systemEventsQuery", err); throw err; }

        if (currentAppUser.groups && currentAppUser.groups.length > 0) {
          try {
            const userGroupEventsQuery = query(collection(db, "events"), where("type", "==", "user"), where("associatedGroupIds", "array-contains-any", currentAppUser.groups.slice(0,30)));
            const userGroupEventsSnap = await getDocs(userGroupEventsQuery);
            userGroupEventsSnap.forEach(doc => {
              if (!relevantEvents.find(e => e.id === doc.id)) {
                relevantEvents.push({ ...doc.data(), id: doc.id, date: String(doc.data().date), createdAt: formatTimestamp(doc.data().createdAt), updatedAt: formatTimestamp(doc.data().updatedAt) } as Event);
              }
            });
          } catch (err) { console.error("Error fetching userGroupEventsQuery", err); throw err; }
        }
        setEvents(relevantEvents.sort((a,b) => (a.date && b.date) ? a.date.localeCompare(b.date) : 0));
      }
      setUsers(fetchedUsers);
      setWishlists(fetchedWishlistShells);
      setAllItems(fetchedItems);

    } catch (error: any) {
      console.error(`Error during ${currentAppUser?.isAdmin ? 'ADMIN' : 'NON-ADMIN'} data fetch:`, error);
      toast({ title: "Data Load Error", description: `Could not load your data. ${getErrorMessage(error)}. Check console and Firestore rules.`, variant: "destructive", duration: 7000 });
      setUsers(currentAppUser ? [currentAppUser] : []);
      setGroupsData([]); setEvents([]); setWishlists([]); setAllItems([]); setMessages([]);
    }
  };

  const updateUserProfileField_internal = async (userId: string, fieldData: Partial<Omit<AppUser, 'id' | 'createdAt' | 'updatedAt' | 'password'>>): Promise<boolean> => {
    if (!db || !currentUser) { toast({ title: "Error", description: "User or database not available.", variant: "destructive" }); return false; }
    if (!currentUser.isAdmin && currentUser.id !== userId) { toast({ title: "Permission Denied", description: "You can only update your own profile.", variant: "destructive" }); return false; }
    try {
        const userDocRef = doc(db, "users", userId);
        const dataToUpdate: any = { ...fieldData, updatedAt: serverTimestamp() };
        if (!currentUser.isAdmin) { delete dataToUpdate.isAdmin; delete dataToUpdate.isApproved; delete dataToUpdate.groups; }
        await updateDoc(userDocRef, dataToUpdate); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating user profile field:", error); toast({ title: "Update Failed", description: "Could not update profile field.", variant: "destructive" }); return false; }
  };

  const signInUser_internal = async (email: string, pass: string): Promise<boolean> => {
    if (!auth) { toast({ title: "Login Error", description: "Authentication service not ready.", variant: "destructive" }); setAuthLoading(false); return false; }
    setAuthLoading(true);
    try { await signInWithEmailAndPassword(auth, email, pass); toast({ title: "Login Successful", description: "Welcome back!" }); return true;
    } catch (error: any) {
      console.error("Firebase Auth Error:", error); let errorMessage = "Login failed. Please check your credentials.";
      if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential') errorMessage = "Invalid email or password.";
      else if (error.code === 'auth/invalid-email') errorMessage = "Please enter a valid email address.";
      else if (error.code === 'auth/too-many-requests') errorMessage = "Access to this account has been temporarily disabled due to many failed login attempts. You can try again later or reset your password.";
      toast({ title: "Login Failed", description: errorMessage, variant: "destructive" }); setAuthLoading(false); return false;
    }
  };

  const signOutUser_internal = async (): Promise<void> => {
    if (!auth) { console.error("Firebase auth instance not available for sign out."); return; }
    setAuthLoading(true);
    try { await signOut(auth); toast({ title: "Logged Out", description: "You have been successfully logged out." });
    } catch (error) { console.error("Error signing out from Firebase:", error); toast({ title: "Logout Failed", description: "Could not sign out.", variant: "destructive" }); }
  };

  const changeUserPassword_internal = async (newPassword: string): Promise<boolean> => {
    if (!firebaseUser || !auth) { toast({ title: "Error", description: "Not logged in.", variant: "destructive" }); return false; }
    try { await firebaseUpdatePassword(firebaseUser, newPassword); toast({ title: "Password Updated", description: "Your GiftLink account password has been successfully updated." }); return true;
    } catch (error: any) {
      console.error("Firebase Change Password Error:", error); let desc = "Could not update password.";
      if (error.code === 'auth/requires-recent-login') desc = "This operation is sensitive and requires recent authentication. Please log out and log back in to continue.";
      else if (error.code === 'auth/weak-password') desc = "Password is too weak (min 6 characters for Firebase).";
      toast({ title: "Password Change Failed", description: desc, variant: "destructive" }); return false;
    }
  };

  const canViewWishlist = useCallback((targetUserId: string): boolean => {
    if (!currentUser) return false;
    if (currentUser.isAdmin) return true;
    if (currentUser.id === targetUserId) return true;
    const targetUser = users.find(u => u.id === targetUserId && u.isApproved);
    if (!targetUser) return false;
    const currentGroups = currentUser.groups || [];
    const targetGroups = targetUser.groups || [];
    return currentGroups.some(g => targetGroups.includes(g));
  }, [currentUser, users]);

  const getWishlistByUserId_internal = async (userId: string): Promise<Wishlist | undefined> => {
    if (!canViewWishlist(userId)) return undefined;
    const existingWishlistShell = wishlists.find(w => w.userId === userId);
    if (existingWishlistShell) return existingWishlistShell;

    if (db) {
        try {
            const wishlistDocRef = doc(db, "wishlists", userId);
            const wishlistDocSnap = await getDoc(wishlistDocRef);
            if (wishlistDocSnap.exists()) {
                const data = wishlistDocSnap.data();
                const fetchedWishlistShell: Wishlist = {
                    id: wishlistDocSnap.id, userId: data.userId || wishlistDocSnap.id, // Ensure userId field
                    createdAt: formatTimestamp(data.createdAt), updatedAt: formatTimestamp(data.updatedAt),
                };
                setWishlists(prev => {
                    if (prev.some(w => w.userId === fetchedWishlistShell.userId)) return prev.map(w => w.userId === fetchedWishlistShell.userId ? fetchedWishlistShell : w);
                    return [...prev, fetchedWishlistShell];
                });
                return fetchedWishlistShell;
            } return undefined;
        } catch (error) { console.error(`Error fetching wishlist shell for user ${userId}:`, error); toast({ title: "Wishlist Load Error", description: `Could not load wishlist for user ${userId}.`, variant: "destructive"}); return undefined; }
    }
    return undefined;
  };

  const getItemsForUser_internal = (userId: string): WishlistItem[] => {
    if (!canViewWishlist(userId)) return [];
    return allItems.filter(item => item.ownerUid === userId);
  };

  const getItemById_internal = (itemId: string): WishlistItem | undefined => {
    return allItems.find(item => item.id === itemId);
  };

  const addItemToCurrentUserWishlist_internal = async (itemDetails: Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    if (!currentUser || !db) { toast({ title: "Error", description: "Please log in to add items.", variant: "destructive" }); return false; }
    const newItemId = uuidv4(); 
    const itemForFirestore: Omit<WishlistItem, 'id'> & { createdAt: FieldValue, updatedAt: FieldValue, dateAdded: FieldValue } = {
      uniqueId: generateUniqueId(),
      ownerUid: currentUser.id, wishlistId: currentUser.id,
      name: itemDetails.name, price: itemDetails.price, retailer: itemDetails.retailer,
      quantity: itemDetails.quantity || 1, receivedQuantity: 0, commitments: [],
      dateAdded: serverTimestamp(), createdAt: serverTimestamp(), updatedAt: serverTimestamp(),
      url: itemDetails.url || undefined, imageUrl: itemDetails.imageUrl || undefined,
      description: itemDetails.description || undefined, categoryId: itemDetails.categoryId || undefined,
      rankId: itemDetails.rankId || undefined,
    };
    
    Object.keys(itemForFirestore).forEach(key => { if ((itemForFirestore as any)[key] === undefined) delete (itemForFirestore as any)[key]; });

    try {
      const newItemDocRef = await addDoc(collection(db, "items"), itemForFirestore);
      // It's good practice to ensure the wishlist shell exists, although onAuthStateChanged should handle it.
      // We won't add items to the wishlist document itself anymore.
      const wishlistDocRef = doc(db, "wishlists", currentUser.id);
      const wishlistSnap = await getDoc(wishlistDocRef);
      if (!wishlistSnap.exists()) {
          await setDoc(wishlistDocRef, { 
              userId: currentUser.id, 
              createdAt: serverTimestamp(), 
              updatedAt: serverTimestamp() 
          });
      } else {
          await updateDoc(wishlistDocRef, { updatedAt: serverTimestamp() }); // Just update timestamp
      }

      await _generateAndDispatchNotifications_internal('item_added', currentUser, { name: itemDetails.name, id: newItemDocRef.id, ownerName: currentUser.name }, currentUser.id);
      await refreshAllDataFromFirestore(); return true;
    } catch (error: any) { console.error("Error adding item to 'items' collection:", error); toast({ title: "Add Item Failed", description: `Could not add item. ${getErrorMessage(error)}`, variant: "destructive" }); return false; }
  };

  const updateItemInCurrentUserWishlist_internal = async (itemId: string, itemDetails: Partial<Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser || !db) return false;
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) { toast({ title: "Error", description: "Item not found or permission denied.", variant: "destructive" }); return false; }
        const originalItem = { ...itemSnap.data(), id: itemSnap.id } as WishlistItem;
        const dataToUpdate: any = { updatedAt: serverTimestamp() };
        
        // Add uniqueId if it doesn't exist
        if (!originalItem.uniqueId) {
          dataToUpdate.uniqueId = generateUniqueId();
        }
        
        // Handle field updates and deletions
        Object.keys(itemDetails).forEach(key => {
          const value = (itemDetails as any)[key];
          if (value === undefined) {
            // Use deleteField() to remove the field from Firestore
            dataToUpdate[key] = deleteField();
          } else {
            dataToUpdate[key] = value;
          }
        });
        
        await updateDoc(itemDocRef, dataToUpdate);
        const changesForNotification = Object.keys(itemDetails).map(key => {
          const oldVal = (originalItem as any)[key];
          const newVal = (itemDetails as any)[key];
          return { 
            field: key, 
            oldValue: oldVal !== undefined ? oldVal.toString() : null, 
            newValue: newVal !== undefined ? newVal.toString() : null 
          };
        }).filter(change => change.oldValue !== change.newValue);
        if (changesForNotification.length > 0) await _generateAndDispatchNotifications_internal('item_updated', currentUser, { name: itemDetails.name || originalItem.name, oldName: originalItem.name, id: itemId, changes: changesForNotification, ownerName: currentUser.name }, currentUser.id);
        await refreshAllDataFromFirestore(); return true;
    } catch (error: any) { console.error("Error updating item in Firestore:", error); toast({ title: "Update Failed", description: `Could not update item. ${getErrorMessage(error)}`, variant: "destructive"}); return false; }
  };

  const removeItemFromCurrentUserWishlist_internal = async (itemId: string): Promise<boolean> => {
    if (!currentUser || !db) return false;
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) { toast({ title: "Error", description: "Item not found or permission denied.", variant: "destructive" }); return false; }
        const itemToRemove = { ...itemSnap.data(), id: itemSnap.id } as WishlistItem;
        await deleteDoc(itemDocRef);
        await _generateAndDispatchNotifications_internal('item_deleted_by_owner', currentUser, { name: itemToRemove.name, id: itemId, ownerName: currentUser.name }, currentUser.id, itemToRemove);
        await refreshAllDataFromFirestore(); return true;
    } catch (error: any) { console.error("Error deleting item from Firestore:", error); toast({ title: "Delete Failed", description: `Could not delete item. ${getErrorMessage(error)}`, variant: "destructive"}); return false; }
  };

  const copyItemToCurrentUserWishlist_internal = async (itemToCopy: WishlistItem): Promise<boolean> => {
    if (!currentUser) return false;
    
    // Check if an item with the same uniqueId already exists in the current user's wishlist
    const existingItem = allItems.find(item => 
      item.ownerUid === currentUser.id && item.uniqueId === itemToCopy.uniqueId
    );
    
    if (existingItem) {
      toast({
        title: "Item Already Exists",
        description: `"${itemToCopy.name}" is already in your wishlist.`,
        variant: "destructive"
      });
      return false;
    }
    
    const { name, price, retailer, url, imageUrl, description, categoryId, rankId, quantity: originalQuantity } = itemToCopy;
    const newItemDetails = { name, price, retailer, url: url || undefined, imageUrl: imageUrl || undefined, description: description || undefined, categoryId: categoryId || undefined, rankId: rankId || undefined, quantity: 1, };
    const cleanedItemDetails: any = {};
    (Object.keys(newItemDetails) as Array<keyof typeof newItemDetails>).forEach(key => { if (newItemDetails[key] !== undefined) cleanedItemDetails[key] = newItemDetails[key]; });
    return await addItemToCurrentUserWishlist_internal(cleanedItemDetails as Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>);
  };

  const updateItemCommitments_internal = async (itemId: string, updateFn: (commitments: Commitment[]) => Commitment[] | null): Promise<boolean> => {
    if (!db) return false;
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists()) { toast({ title: "Error", description: "Item not found for commitment.", variant: "destructive"}); return false; }
        const itemData = itemSnap.data() as WishlistItem;
        const currentCommitments = (Array.isArray(itemData.commitments) ? itemData.commitments : []).map(c => ({ ...c, createdAt: c.createdAt ? formatTimestamp(c.createdAt) || new Date().toISOString() : new Date().toISOString(), id: c.id || c.commitmentId || uuidv4(), }));
        const newCommitmentsArray = updateFn(currentCommitments);
        if (newCommitmentsArray === null) return true; 

        // Use ISO string for createdAt in array, only use serverTimestamp for top-level updatedAt
        const updateData: any = { 
          commitments: newCommitmentsArray.map(c => ({...c, createdAt: c.createdAt || new Date().toISOString()})), 
          updatedAt: serverTimestamp() 
        };
        
        // Add uniqueId if it doesn't exist
        if (!itemData.uniqueId) {
          updateData.uniqueId = generateUniqueId();
        }
        
        await updateDoc(itemDocRef, updateData); 
        return true;
    } catch (error) { console.error("Error in updateItemCommitments_internal:", error); toast({ title: "Commitment Error", description: "Could not update item commitment.", variant: "destructive"}); return false; }
  };

  const reserveItem_internal = async (itemOwnerUserId: string, itemId: string, quantity: number): Promise<boolean> => {
    if (!currentUser) { toast({ title: "Error", description: "Please log in.", variant: "destructive" }); return false; }
    const success = await updateItemCommitments_internal(itemId, (commitments) => {
      const existingReservation = commitments.find(c => c.committedByUid === currentUser.id && c.type === 'reserved');
      if (existingReservation) return commitments.map(c => c.id === existingReservation.id ? { ...c, quantity: (c.quantity || 0) + quantity, createdAt: new Date().toISOString() } : c);
      else return [...commitments, { id: uuidv4(), committedByUid: currentUser.id, quantity, type: 'reserved', createdAt: new Date().toISOString() }];
    });
    if (success) await refreshAllDataFromFirestore(); return success;
  };

  const unreserveItem_internal = async (itemOwnerUserId: string, itemId: string, quantityToUnreserve: number, commitmentIdToModify?: string): Promise<boolean> => {
    if (!currentUser) { toast({ title: "Error", description: "Please log in.", variant: "destructive" }); return false; }
    const success = await updateItemCommitments_internal(itemId, (commitments) => {
        const targetId = commitmentIdToModify || commitments.find(c => c.committedByUid === currentUser.id && c.type === 'reserved')?.id;
        if (!targetId) return commitments;
        return commitments.map(c => {
            if (c.id === targetId && c.type === 'reserved') {
                const newQuantity = Math.max(0, (c.quantity || 0) - quantityToUnreserve);
                return newQuantity > 0 ? { ...c, quantity: newQuantity, createdAt: new Date().toISOString() } : null;
            } return c;
        }).filter(Boolean) as Commitment[];
    });
    if (success) await refreshAllDataFromFirestore(); return success;
  };

  const purchaseItem_internal = async (itemOwnerUserId: string, itemId: string, quantity: number, sourceCommitmentId?: string): Promise<boolean> => {
    if (!currentUser) { toast({ title: "Error", description: "Please log in.", variant: "destructive" }); return false; }
    const success = await updateItemCommitments_internal(itemId, (commitments) => {
      let updatedCommitments = [...commitments]; let quantityToPurchase = quantity;
      if (sourceCommitmentId) {
        const reservationIndex = updatedCommitments.findIndex(c => c.id === sourceCommitmentId && c.type === 'reserved' && c.committedByUid === currentUser.id);
        if (reservationIndex > -1) {
          const reservation = updatedCommitments[reservationIndex]; const quantityFromReservation = Math.min(quantityToPurchase, reservation.quantity || 0);
          if ((reservation.quantity || 0) - quantityFromReservation > 0) updatedCommitments[reservationIndex] = { ...reservation, quantity: (reservation.quantity || 0) - quantityFromReservation, createdAt: new Date().toISOString() };
          else updatedCommitments.splice(reservationIndex, 1);
          const existingPurchase = updatedCommitments.find(c => c.committedByUid === currentUser.id && c.type === 'purchased');
          if (existingPurchase) updatedCommitments = updatedCommitments.map(c => c.id === existingPurchase.id ? { ...c, quantity: (c.quantity || 0) + quantityFromReservation, createdAt: new Date().toISOString() } : c);
          else updatedCommitments.push({ id: uuidv4(), committedByUid: currentUser.id, quantity: quantityFromReservation, type: 'purchased', createdAt: new Date().toISOString() });
          quantityToPurchase -= quantityFromReservation;
        }
      }
      if (quantityToPurchase > 0) {
        const existingPurchase = updatedCommitments.find(c => c.committedByUid === currentUser.id && c.type === 'purchased');
        if (existingPurchase) updatedCommitments = updatedCommitments.map(c => c.id === existingPurchase.id ? { ...c, quantity: (c.quantity || 0) + quantityToPurchase, createdAt: new Date().toISOString() } : c);
        else updatedCommitments.push({ id: uuidv4(), committedByUid: currentUser.id, quantity: quantityToPurchase, type: 'purchased', createdAt: new Date().toISOString() });
      } return updatedCommitments;
    });
    if (success) await refreshAllDataFromFirestore(); return success;
  };

  const returnItem_internal = async (itemOwnerUserId: string, itemId: string, quantityToReturn: number, commitmentIdToModify?: string): Promise<boolean> => {
    if (!currentUser) { toast({ title: "Error", description: "Please log in.", variant: "destructive" }); return false; }
    const success = await updateItemCommitments_internal(itemId, (commitments) => {
        const targetId = commitmentIdToModify || commitments.find(c => c.committedByUid === currentUser.id && c.type === 'purchased')?.id;
        if (!targetId) return commitments;
        return commitments.map(c => {
            if (c.id === targetId && c.type === 'purchased') {
                const newQuantity = Math.max(0, (c.quantity || 0) - quantityToReturn);
                return newQuantity > 0 ? { ...c, quantity: newQuantity, createdAt: new Date().toISOString() } : null;
            } return c;
        }).filter(Boolean) as Commitment[];
    });
    if (success) await refreshAllDataFromFirestore(); return success;
  };

  const markItemAsReceived_internal = async (itemId: string, quantityReceived: number): Promise<boolean> => {
    if (!currentUser || !db) return false;
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) { toast({title: "Error", description: "Item not found or permission denied.", variant: "destructive"}); return false; }
        const itemData = itemSnap.data() as WishlistItem;
        const currentReceived = itemData.receivedQuantity || 0;
        const newReceived = Math.min(itemData.quantity, currentReceived + quantityReceived);
        const updateData: any = { receivedQuantity: newReceived, updatedAt: serverTimestamp() };
        
        // Add uniqueId if it doesn't exist
        if (!itemData.uniqueId) {
          updateData.uniqueId = generateUniqueId();
        }
        
        await updateDoc(itemDocRef, updateData);
        await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error marking item as received:", error); toast({title: "Update Failed", description: "Could not mark item as received.", variant: "destructive"}); return false; }
  };

  const unmarkItemAsReceived_internal = async (itemId: string, quantityToUnreceive: number): Promise<boolean> => {
    if (!currentUser || !db) return false;
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) { toast({title: "Error", description: "Item not found or permission denied.", variant: "destructive"}); return false; }
        const itemData = itemSnap.data() as WishlistItem;
        const newReceived = Math.max(0, (itemData.receivedQuantity || 0) - quantityToUnreceive);
        const updateData: any = { receivedQuantity: newReceived, updatedAt: serverTimestamp() };
        
        // Add uniqueId if it doesn't exist
        if (!itemData.uniqueId) {
          updateData.uniqueId = generateUniqueId();
        }
        
        await updateDoc(itemDocRef, updateData);
        await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error unmarking item as received:", error); toast({title: "Update Failed", description: "Could not unmark item as received.", variant: "destructive"}); return false; }
  };

  const getUnreadMessagesCount_internal = (userId: string): number => messages.filter(msg => msg.recipientUid === userId && !msg.isRead).length;
  const getMessagesForUser_internal = (userId: string): Message[] => messages.filter(msg => msg.recipientUid === userId).sort((a, b) => { const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0; const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0; return timeB - timeA; });
  const getAllMessages_internal = (): Message[] => [...messages].sort((a, b) => { const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0; const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0; return timeB - timeA; });
  const markMessagesAsRead_internal = async (userId: string, messageIdsParam?: string[]): Promise<void> => {
    if (!db) return;
    const idsToMark = messageIdsParam || messages.filter(msg => msg.recipientUid === userId && !msg.isRead).map(msg => msg.id);
    if (idsToMark.length === 0) return;
    const batch = writeBatch(db);
    idsToMark.forEach(id => { batch.update(doc(db, "messages", id), { isRead: true, updatedAt: serverTimestamp() }); });
    try { await batch.commit(); setMessages(prev => prev.map(msg => idsToMark.includes(msg.id) ? { ...msg, isRead: true, updatedAt: new Date().toISOString() } : msg));
    } catch (error) { console.error("Error marking messages as read in Firestore:", error); }
  };
  const deleteNotificationById_internal = async (messageId: string): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await deleteDoc(doc(db, "messages", messageId)); setMessages(prev => prev.filter(msg => msg.id !== messageId)); return true;
    } catch (error) { console.error("Error deleting notification by ID:", error); toast({ title: "Delete Failed", description: "Could not delete the notification.", variant: "destructive" }); return false; }
  };
  const deleteAllNotificationsForUser_internal = async (userId: string): Promise<boolean> => {
    if (!db) return false;
    try {
      const batch = writeBatch(db);
      const userMessagesQuery = query(collection(db, "messages"), where("recipientUid", "==", userId));
      const userMessagesSnap = await getDocs(userMessagesQuery);
      userMessagesSnap.docs.forEach(docSnap => batch.delete(docSnap.ref));
      await batch.commit();
      setMessages(prev => prev.filter(msg => msg.recipientUid !== userId));
      return true;
    } catch (error) {
      console.error("Error deleting user notifications:", error);
      toast({ title: "Delete Failed", description: "Could not delete your notifications. Please try again.", variant: "destructive" });
      return false;
    }
  };

  const deleteAllNotificationsAndNotifyUsers_internal = async (): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try {
      const batch = writeBatch(db); const messagesSnap = await getDocs(collection(db, "messages"));
      messagesSnap.docs.forEach(docSnap => batch.delete(docSnap.ref));
      const allUserDocsSnap = await getDocs(collection(db, "users"));
      const resetContent = `Notification history was reset by an admin on ${format(new Date(), "PPP p")}.`;
      allUserDocsSnap.forEach(userDocSnap => {
          const messageDataForReset: Partial<Omit<Message, 'id'>> & { timestamp: FieldValue; updatedAt: FieldValue; isRead: boolean } = {
            recipientUid: userDocSnap.id, actingUid: "system", actingUserName: "System Admin",
            eventType: "system_notification_reset", content: resetContent, isRead: false,
            timestamp: serverTimestamp(), updatedAt: serverTimestamp()
          };
          batch.set(doc(collection(db, "messages")), messageDataForReset);
      });
      await batch.commit(); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error deleting all notifications and notifying users:", error); toast({ title: "Action Failed", description: "Could not delete all notifications.", variant: "destructive" }); return false; }
  };

  const getUpcomingEventsForUser_internal = (userId: string): UpcomingEventDisplayInfo[] => {
    const userProfile = users.find(u => u.id === userId); if (!userProfile) return [];
    const userGroupIds = userProfile.groups || []; const now = startOfDay(new Date());
    const endDateLimit = addMonths(now, upcomingEventsDisplayMonths); const currentYear = getYear(now);
    return events.filter(event => {
        if (!event.type || !event.date) return false; if (event.type === 'system') return true;
        if (event.type === 'user' && userGroupIds.some(ugid => (event.associatedGroupIds || []).includes(ugid))) return true;
        return false;
      }).map(event => {
        let displayDate: Date | null = null;
        try {
            if (event.isRecurringYearly) {
                const [monthStr, dayStr] = event.date.split('-'); const month = parseInt(monthStr, 10) -1; const day = parseInt(dayStr, 10);
                if (isNaN(month) || isNaN(day) || month < 0 || month > 11 || day < 1 || day > 31) throw new Error("Invalid recurring date format: " + event.date);
                let nextOccurrenceThisYear = setYear(new Date(currentYear, month, day), currentYear);
                if (nextOccurrenceThisYear < now && differenceInDays(now, nextOccurrenceThisYear) > 0 ) displayDate = setYear(nextOccurrenceThisYear, currentYear + 1);
                else displayDate = nextOccurrenceThisYear;
            } else { const parsedNonRecurring = parse(event.date, 'yyyy-MM-dd', new Date()); if (isValid(parsedNonRecurring)) displayDate = parsedNonRecurring; else throw new Error("Invalid non-recurring date format: " + event.date); }
        } catch (e: any) { console.error(`Error parsing event date for event "${event.name}" (ID: ${event.id}, Date string: "${event.date}"):`, e.message); return null; }
        return displayDate && isValid(displayDate) ? { ...event, displayDate, originalEventDate: event.date } : null;
      }).filter(eventInfo => eventInfo && eventInfo.displayDate && isWithinInterval(eventInfo.displayDate, { start: now, end: endDateLimit }))
      .sort((a, b) => a!.displayDate.getTime() - b!.displayDate.getTime()) as UpcomingEventDisplayInfo[];
  };
  const addUserEvent_internal = async (eventData: Omit<Event, 'id' | 'type' | 'createdAt' | 'updatedAt' | 'createdByUserId'>): Promise<boolean> => {
    if (!currentUser || !currentUser.isApproved || !db) return false;
    const dataToAdd: Omit<Event, 'id'> = { ...eventData, type: 'user', createdByUserId: currentUser.id, createdAt: serverTimestamp() as any, updatedAt: serverTimestamp() as any, };
    try { await addDoc(collection(db, "events"), dataToAdd); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error adding user event:", error); toast({title: "Event Error", description: "Could not add event.", variant: "destructive"}); return false; }
  };
  const addAdminEvent_internal = async (eventData: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    const dataToAdd: Omit<Event, 'id'> = { ...eventData, createdAt: serverTimestamp() as any, updatedAt: serverTimestamp() as any };
    try { await addDoc(collection(db, "events"), dataToAdd); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error adding admin event:", error); toast({title: "Event Error", description: "Could not add event.", variant: "destructive"}); return false; }
  };
  const updateAdminEvent_internal = async (eventId: string, eventData: Partial<Omit<Event, 'id' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { const dataToUpdate: any = { ...eventData, updatedAt: serverTimestamp() }; await updateDoc(doc(db, "events", eventId), dataToUpdate); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating admin event:", error); toast({title: "Event Error", description: "Could not update event.", variant: "destructive"}); return false; }
  };
  const deleteAdminEvent_internal = async (eventId: string): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await deleteDoc(doc(db, "events", eventId)); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error deleting admin event:", error); toast({title: "Event Error", description: "Could not delete event.", variant: "destructive"}); return false; }
  };

  const addCategory_internal = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await addDoc(collection(db, "categories"), { ...categoryData, createdAt: serverTimestamp(), updatedAt: serverTimestamp() }); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error adding category:", error); toast({title: "Category Error", description: "Could not add category.", variant: "destructive"}); return false; }
  };
  const updateCategory_internal = async (categoryId: string, categoryData: Partial<Omit<Category, 'id' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await updateDoc(doc(db, "categories", categoryId), { ...categoryData, updatedAt: serverTimestamp() }); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating category:", error); toast({title: "Category Error", description: "Could not update category.", variant: "destructive"}); return false; }
  };
  const deleteCategory_internal = async (categoryId: string): Promise<{ success: boolean; message?: string }> => {
    if (!currentUser?.isAdmin || !db) return { success: false, message: "Permission denied." };
    const itemsUsingThisCategory = allItems.filter(item => item.categoryId === categoryId);
    if (itemsUsingThisCategory.length > 0) return { success: false, message: `Cannot delete. ${itemsUsingThisCategory.length} item(s) use this category.` };
    try { await deleteDoc(doc(db, "categories", categoryId)); await refreshAllDataFromFirestore(); return { success: true };
    } catch (error) { console.error("Error deleting category:", error); return { success: false, message: "Could not delete category." }; }
  };

  const addGroup_internal = async (groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await addDoc(collection(db, "groups"), { ...groupData, createdAt: serverTimestamp(), updatedAt: serverTimestamp() }); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error adding group:", error); toast({title: "Group Error", description: "Could not add group.", variant: "destructive"}); return false; }
  };
  const updateGroup_internal = async (groupId: string, groupData: Partial<Omit<Group, 'id' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await updateDoc(doc(db, "groups", groupId), { ...groupData, updatedAt: serverTimestamp() }); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating group:", error); toast({title: "Group Error", description: "Could not update group.", variant: "destructive"}); return false; }
  };
  const deleteGroup_internal = async (groupId: string): Promise<{ success: boolean; message?: string }> => {
    if (!currentUser?.isAdmin || !db) return { success: false, message: "Permission denied." };
    const usersInGroupQuery = query(collection(db, "users"), where("groups", "array-contains", groupId));
    const usersInGroupSnap = await getDocs(usersInGroupQuery);
    if (!usersInGroupSnap.empty) return { success: false, message: `Cannot delete. ${usersInGroupSnap.size} user(s) in this group.` };
    const eventsInGroupQuery = query(collection(db, "events"), where("associatedGroupIds", "array-contains-any", [groupId]));
    const eventsSnap = await getDocs(eventsInGroupQuery);
    if (!eventsSnap.empty) return { success: false, message: `Cannot delete. ${eventsSnap.size} event(s) use this group.` };
    try { await deleteDoc(doc(db, "groups", groupId)); await refreshAllDataFromFirestore(); return { success: true };
    } catch (error) { console.error("Error deleting group:", error); return { success: false, message: "Could not delete group." }; }
  };

  const addRank_internal = async (rankData: Omit<Rank, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
     if (!currentUser?.isAdmin || !db) return false;
    try { await addDoc(collection(db, "ranks"), { ...rankData, createdAt: serverTimestamp(), updatedAt: serverTimestamp() }); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error adding rank:", error); toast({title: "Rank Error", description: "Could not add rank.", variant: "destructive"}); return false; }
  };
  const updateRank_internal = async (rankId: string, rankData: Partial<Omit<Rank, 'id' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    try { await updateDoc(doc(db, "ranks", rankId), { ...rankData, updatedAt: serverTimestamp() }); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating rank:", error); toast({title: "Rank Error", description: "Could not update rank.", variant: "destructive"}); return false; }
  };
  const deleteRank_internal = async (rankId: string): Promise<{ success: boolean; message?: string }> => {
    if (!currentUser?.isAdmin || !db) return { success: false, message: "Permission denied." };
    const itemsUsingThisRank = allItems.filter(item => item.rankId === rankId);
    if (itemsUsingThisRank.length > 0) return { success: false, message: `Cannot delete. ${itemsUsingThisRank.length} item(s) use this rank.` };
    try { await deleteDoc(doc(db, "ranks", rankId)); await refreshAllDataFromFirestore(); return { success: true };
    } catch (error) { console.error("Error deleting rank:", error); return { success: false, message: "Could not delete rank." }; }
  };

  const addUser_internal = async (userData: Omit<AppUser, 'id' | 'avatarUrl' | 'createdAt' | 'updatedAt'> & { password?: string }): Promise<{success: boolean; newUserId?: string}> => {
    const result = await addAuthUserAndProfile(userData, currentUser);
    
    // Handle post-creation actions that were in the original implementation
    if (result.success) {
      // Check if we need to sign out (if somehow the admin got signed in as the new user)
      if (auth?.currentUser?.uid === result.newUserId) {
        await signOut(auth);
      } else {
        // Refresh data to include the new user
        await refreshAllDataFromFirestore();
      }
    }
    
    return { success: result.success, newUserId: result.newUserId };
  };

  const updateUser_internal = async (userId: string, userData: Partial<Omit<AppUser, 'id' | 'avatarUrl' | 'password' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) return false;
    const userToUpdateDocRef = doc(db, "users", userId);
    try {
        const userToUpdateSnap = await getDoc(userToUpdateDocRef);
        if (!userToUpdateSnap.exists()) { toast({ title: "Update Failed", description: "User not found.", variant: "destructive"}); return false; }
        const existingUserData = userToUpdateSnap.data() as AppUser;
        if (userData.email && userData.email !== existingUserData.email) {
            const q = query(collection(db, "users"), where("email", "==", userData.email));
            const querySnapshot = await getDocs(q);
            if (!querySnapshot.empty && querySnapshot.docs[0].id !== userId) { toast({ title: "Update Failed", description: `Email "${userData.email}" is already in use.`, variant: "destructive"}); return false; }
        }
        const dataToUpdate: any = { ...userData, updatedAt: serverTimestamp() };
        if (userData.email === undefined) dataToUpdate.email = existingUserData.email;
        await updateDoc(userToUpdateDocRef, dataToUpdate); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating user:", error); toast({ title: "Update Failed", description: "Could not update user.", variant: "destructive" }); return false; }
  };

  const deleteUser_internal = async (userIdToDelete: string): Promise<{ success: boolean; message?: string }> => {
    if (!currentUser?.isAdmin || !db || !auth.currentUser) return { success: false, message: "Permission denied." };
    if (auth.currentUser.uid === userIdToDelete) return { success: false, message: "Admins cannot delete their own account." };
    const userToDeleteProfile = users.find(u => u.id === userIdToDelete);
    if (!userToDeleteProfile) return { success: false, message: "User profile not found." };
    if (userToDeleteProfile.isAdmin) { const otherAdmins = users.filter(u => u.isAdmin && u.id !== userIdToDelete); if (otherAdmins.length === 0) return { success: false, message: "Cannot delete the last admin user." }; }
    try {
      const batch = writeBatch(db);
      batch.delete(doc(db, "users", userIdToDelete));
      batch.delete(doc(db, "wishlists", userIdToDelete)); 

      const itemsToDeleteSnap = await getDocs(query(collection(db, "items"), where("ownerUid", "==", userIdToDelete)));
      let itemsWithCommitments: WishlistItem[] = [];
      itemsToDeleteSnap.forEach(itemDoc => {
        batch.delete(itemDoc.ref);
        const itemData = itemDoc.data() as WishlistItem;
        if (itemData.commitments && itemData.commitments.length > 0) {
          itemsWithCommitments.push({ ...itemData, id: itemDoc.id });
        }
      });

      for (const item of itemsWithCommitments) {
        await _generateAndDispatchNotifications_internal('user_deleted_commitment_impacted', currentUser, { name: item.name, id: item.id }, userIdToDelete, item, userToDeleteProfile.name);
      }
      
      const allOtherItemsSnap = await getDocs(query(collection(db, "items"), where("ownerUid", "!=", userIdToDelete)));
      allOtherItemsSnap.forEach(itemDoc => {
        const itemData = itemDoc.data() as WishlistItem;
        if (Array.isArray(itemData.commitments)) {
          const originalCommitmentsLength = itemData.commitments.length;
          const filteredCommitments = itemData.commitments.filter(c => c.committedByUid !== userIdToDelete);
          if (filteredCommitments.length !== originalCommitmentsLength) {
            const updateData: any = { commitments: filteredCommitments, updatedAt: serverTimestamp() };
            
            // Add uniqueId if it doesn't exist
            if (!itemData.uniqueId) {
              updateData.uniqueId = generateUniqueId();
            }
            
            batch.update(itemDoc.ref, updateData);
          }
        }
      });

      const messagesToUserSnap = await getDocs(query(collection(db, "messages"), where("recipientUid", "==", userIdToDelete)));
      messagesToUserSnap.forEach(msgDoc => batch.delete(msgDoc.ref));
      const messagesFromUserSnap = await getDocs(query(collection(db, "messages"), where("actingUid", "==", userIdToDelete)));
      messagesFromUserSnap.forEach(msgDoc => batch.delete(msgDoc.ref));
      const eventsByUserSnap = await getDocs(query(collection(db, "events"), where("createdByUserId", "==", userIdToDelete)));
      eventsByUserSnap.forEach(eventDoc => batch.delete(eventDoc.ref));
      await batch.commit();
      toast({ title: "User Data Deleted", description: `Firestore data for ${userToDeleteProfile.name} deleted. Firebase Auth user must be deleted separately.`});
      await refreshAllDataFromFirestore(); return { success: true };
    } catch (error: any) { console.error("Error deleting user data:", error); toast({ title: "Delete User Failed", description: `Could not delete user data. ${getErrorMessage(error)}`, variant: "destructive"}); return { success: false, message: "Could not delete user data from Firestore." }; }
  };

  const updateAnyWishlistItem_internal = async (itemId: string, itemDetails: Partial<Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) { toast({ title: "Permission Denied", variant: "destructive"}); return false;}
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists()) { toast({ title: "Error", description: "Item not found.", variant: "destructive" }); return false; }
        const originalItem = { ...itemSnap.data(), id: itemSnap.id } as WishlistItem;
        const dataToUpdate: any = { updatedAt: serverTimestamp() };
        
        // Add uniqueId if it doesn't exist
        if (!originalItem.uniqueId) {
          dataToUpdate.uniqueId = generateUniqueId();
        }
        
        // Handle field updates and deletions
        Object.keys(itemDetails).forEach(key => {
          const value = (itemDetails as any)[key];
          if (value === undefined) {
            // Use deleteField() to remove the field from Firestore
            dataToUpdate[key] = deleteField();
          } else {
            dataToUpdate[key] = value;
          }
        });
        
        await updateDoc(itemDocRef, dataToUpdate);
        if (currentUser) {
            const changesForNotification = Object.keys(itemDetails).map(key => {
              const oldVal = (originalItem as any)[key];
              const newVal = (itemDetails as any)[key];
              return { 
                field: key, 
                oldValue: oldVal !== undefined ? oldVal.toString() : null, 
                newValue: newVal !== undefined ? newVal.toString() : null 
              };
            }).filter(change => change.oldValue !== change.newValue);
            if (changesForNotification.length > 0) {
                const itemOwnerProfile = users.find(u => u.id === originalItem.ownerUid);
                await _generateAndDispatchNotifications_internal('item_updated_by_admin', currentUser, { id: itemId, name: itemDetails.name || originalItem.name, changes: changesForNotification, ownerName: itemOwnerProfile?.name || 'Unknown Owner' }, originalItem.ownerUid);
            }
        }
        await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error updating item (admin):", error); toast({ title: "Update Failed", description: `Could not update item. ${getErrorMessage(error)}`, variant: "destructive"}); return false; }
  };

  const deleteAnyWishlistItemByAdmin_internal = async (itemId: string, adminReason: string): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) { toast({ title: "Permission Denied", variant: "destructive"}); return false;}
    const itemDocRef = doc(db, "items", itemId);
    try {
        const itemSnap = await getDoc(itemDocRef);
        if (!itemSnap.exists()) { toast({ title: "Error", description: "Item to delete not found.", variant: "destructive" }); return false; }
        const itemToRemove = { ...itemSnap.data(), id: itemSnap.id } as WishlistItem;
        await deleteDoc(itemDocRef);
        if (currentUser) {
            const itemOwnerProfile = users.find(u => u.id === itemToRemove.ownerUid);
            await _generateAndDispatchNotifications_internal('item_deleted_by_admin', currentUser, { name: itemToRemove.name, id: itemId, ownerName: itemOwnerProfile?.name || 'Unknown Owner' }, itemToRemove.ownerUid, itemToRemove, undefined, adminReason);
        }
        await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error deleting item (admin):", error); toast({ title: "Delete Failed", description: `Could not delete item. ${getErrorMessage(error)}`, variant: "destructive"}); return false; }
  };

  const deleteAllWishlistItemsFromAllUsers_internal = async (): Promise<boolean> => {
    if (!currentUser?.isAdmin || !db) { toast({ title: "Permission Denied", description: "Admin only.", variant: "destructive" }); return false; }
    try {
      const batch = writeBatch(db); const itemsSnapshot = await getDocs(collection(db, "items"));
      itemsSnapshot.forEach(itemDoc => { batch.delete(itemDoc.ref); });
      await batch.commit(); await refreshAllDataFromFirestore(); return true;
    } catch (error) { console.error("Error deleting all items:", error); toast({ title: "Operation Failed", description: "Could not delete all items.", variant: "destructive" }); return false; }
  };

  const refreshAllDataFromFirestore = useCallback(async () => {
    const currentFbUser = auth?.currentUser; let currentAppProfile: AppUser | null = null;
    if (currentFbUser && db) {
        try {
            const userDocRef = doc(db, "users", currentFbUser.uid);
            const userDocSnap = await getDoc(userDocRef);
            if (userDocSnap.exists()) {
                const data = userDocSnap.data();
                currentAppProfile = {
                    id: userDocSnap.id, name: data.name || currentFbUser.displayName || "User", email: data.email || currentFbUser.email || "",
                    avatarUrl: data.avatarUrl || currentFbUser.photoURL || "", allowEmails: data.allowEmails === undefined ? false : data.allowEmails,
                    displayEmailToGroupMembers: data.displayEmailToGroupMembers === undefined ? true : data.displayEmailToGroupMembers,
                    isApproved: data.isApproved === undefined ? true : data.isApproved, isAdmin: data.isAdmin || false, groups: data.groups || [],
                    createdAt: formatTimestamp(data.createdAt), updatedAt: formatTimestamp(data.updatedAt),
                } as AppUser;
            }
        } catch (profileError) { console.error("Error fetching current user profile in refreshAllDataFromFirestore:", profileError); }
    }
    await fetchAllData_internal(currentFbUser, currentAppProfile);
  }, []);

  useEffect(() => {
    if (!auth || !db) { console.warn("Firebase auth or db not initialized."); setAuthLoading(false); return; }
    const unsubscribe = onAuthStateChanged(auth, async (fbUser) => {
      setAuthLoading(true); let appUserProfile: AppUser | null = null;
      try {
        if (fbUser) {
          setFirebaseUser(fbUser); const userDocRef = doc(db, "users", fbUser.uid); const userDocSnap = await getDoc(userDocRef);
          if (userDocSnap.exists()) {
            const data = userDocSnap.data();
            appUserProfile = {
              id: userDocSnap.id, name: data.name || fbUser.displayName || "User", email: data.email || fbUser.email || "",
              avatarUrl: data.avatarUrl || fbUser.photoURL || "", allowEmails: data.allowEmails === undefined ? false : data.allowEmails,
              displayEmailToGroupMembers: data.displayEmailToGroupMembers === undefined ? true : data.displayEmailToGroupMembers,
              isApproved: data.isApproved === undefined ? true : data.isApproved, isAdmin: data.isAdmin || false, groups: data.groups || [],
              createdAt: formatTimestamp(data.createdAt), updatedAt: formatTimestamp(data.updatedAt),
            } as AppUser;
          } else {
            const derivedName = fbUser.displayName || (fbUser.email ? fbUser.email.split('@')[0] : "New User") || "GiftLink User";
            const nowForClient = new Date().toISOString();
            const newUserProfileDataForFirestore: Omit<AppUser, 'id'| 'createdAt' | 'updatedAt'> & {createdAt: FieldValue, updatedAt: FieldValue} = {
              name: derivedName, email: fbUser.email || "", avatarUrl: fbUser.photoURL || "", allowEmails: false, displayEmailToGroupMembers: true,
              isApproved: true, isAdmin: false, groups: [], createdAt: serverTimestamp(), updatedAt: serverTimestamp(),
            };
            appUserProfile = { id: fbUser.uid, ...newUserProfileDataForFirestore, createdAt: nowForClient, updatedAt: nowForClient } as AppUser;
            const batch = writeBatch(db); 
            batch.set(doc(db, "users", fbUser.uid), newUserProfileDataForFirestore);
            // Ensure wishlist shell is created with userId
            batch.set(doc(db, "wishlists", fbUser.uid), { 
                userId: fbUser.uid, // Explicitly set userId here
                createdAt: serverTimestamp(), 
                updatedAt: serverTimestamp() 
            });
            await batch.commit(); 
            toast({ title: "Welcome!", description: "Your GiftLink profile has been automatically set up." });
          }
          setCurrentUser(appUserProfile); await fetchAllData_internal(fbUser, appUserProfile);
        } else {
          setCurrentUser(null); setFirebaseUser(null);
          setUsers([]); setGroupsData([]); setEvents([]); setWishlists([]); setAllItems([]); setMessages([]);
          setLastEmailSentTimestamps({}); await fetchAllData_internal(null, null);
        }
      } catch (error: any) {
        console.error("Error in onAuthStateChanged (fetching/creating profile or during fetchAllData):", error);
        toast({ title: "Profile/Data Load Error", description: `Could not load or initialize your profile: ${getErrorMessage(error)}`, variant: "destructive" });
        setCurrentUser(null); setFirebaseUser(fbUser);
        setUsers([]); setGroupsData([]); setEvents([]); setWishlists([]); setAllItems([]); setMessages([]);
        await fetchAllData_internal(fbUser, null);
      } finally { setAuthLoading(false); }
    });
    return () => { unsubscribe(); };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Removed db from dependencies as it should be stable after init

  const addMessageToList = useCallback(addMessageToList_internal, [toast, setMessages]); // db removed
  const _generateAndDispatchNotifications = useCallback(_generateAndDispatchNotifications_internal, [users, addMessageToList_internal, lastEmailSentTimestamps, setLastEmailSentTimestamps]); // db removed from addMessageToList_internal
  const updateUserProfileField = useCallback(updateUserProfileField_internal, [toast, currentUser, refreshAllDataFromFirestore]); // db removed
  const signInUser = useCallback(signInUser_internal, [toast, setAuthLoading]); // auth removed
  const signOutUser = useCallback(signOutUser_internal, [toast, setAuthLoading]); // auth removed
  const changeUserPassword = useCallback(changeUserPassword_internal, [firebaseUser, toast]); // auth removed
  const getWishlistByUserId = useCallback(getWishlistByUserId_internal, [wishlists, setWishlists, toast, canViewWishlist]); // db removed
  const getItemsForUser = useCallback(getItemsForUser_internal, [allItems, canViewWishlist]);
  const getItemById = useCallback(getItemById_internal, [allItems]);
  const addItemToCurrentUserWishlist = useCallback(addItemToCurrentUserWishlist_internal, [currentUser, toast, _generateAndDispatchNotifications, refreshAllDataFromFirestore]); // db removed
  const updateItemInCurrentUserWishlist = useCallback(updateItemInCurrentUserWishlist_internal, [currentUser, toast, _generateAndDispatchNotifications, refreshAllDataFromFirestore]); // db removed
  const removeItemFromCurrentUserWishlist = useCallback(removeItemFromCurrentUserWishlist_internal, [currentUser, toast, _generateAndDispatchNotifications, refreshAllDataFromFirestore]); // db removed
  const copyItemToCurrentUserWishlist = useCallback(copyItemToCurrentUserWishlist_internal, [currentUser, addItemToCurrentUserWishlist_internal]);
  const updateItemCommitments = useCallback(updateItemCommitments_internal, [toast]); // db removed
  const reserveItem = useCallback(reserveItem_internal, [currentUser, toast, updateItemCommitments_internal, refreshAllDataFromFirestore]);
  const unreserveItem = useCallback(unreserveItem_internal, [currentUser, toast, updateItemCommitments_internal, refreshAllDataFromFirestore]);
  const purchaseItem = useCallback(purchaseItem_internal, [currentUser, toast, updateItemCommitments_internal, refreshAllDataFromFirestore]);
  const returnItem = useCallback(returnItem_internal, [currentUser, toast, updateItemCommitments_internal, refreshAllDataFromFirestore]);
  const markItemAsReceived = useCallback(markItemAsReceived_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const unmarkItemAsReceived = useCallback(unmarkItemAsReceived_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const updateUserEmail = useCallback(async (newEmail: string) => { if (!currentUser) return false; return await updateUserProfileField(currentUser.id, { email: newEmail }); }, [currentUser, updateUserProfileField]);
  const updateUserAvatar = useCallback(async (avatarUrl: string) => { if (!currentUser) return false; return await updateUserProfileField(currentUser.id, { avatarUrl }); }, [currentUser, updateUserProfileField]);
  const updateUserAllowEmailsPreference = useCallback(async (allowVal: boolean) => { if (!currentUser) return false; return await updateUserProfileField(currentUser.id, { allowEmails: allowVal }); }, [currentUser, updateUserProfileField]);
  const updateUserDisplayEmailPreference = useCallback(async (displayVal: boolean) => { if (!currentUser) return false; return await updateUserProfileField(currentUser.id, { displayEmailToGroupMembers: displayVal }); }, [currentUser, updateUserProfileField]);
  const getUnreadMessagesCount = useCallback(getUnreadMessagesCount_internal, [messages]);
  const getMessagesForUser = useCallback(getMessagesForUser_internal, [messages]);
  const getAllMessages = useCallback(getAllMessages_internal, [messages]);
  const markMessagesAsRead = useCallback(markMessagesAsRead_internal, [messages, setMessages]); // db removed
  const deleteNotificationById = useCallback(deleteNotificationById_internal, [currentUser, toast, setMessages]); // db removed
  const deleteAllNotificationsForUser = useCallback(deleteAllNotificationsForUser_internal, [toast, setMessages]); // db removed
  const deleteAllNotificationsAndNotifyUsers = useCallback(deleteAllNotificationsAndNotifyUsers_internal, [currentUser, addMessageToList_internal, refreshAllDataFromFirestore, toast]); // db removed
  const getUpcomingEventsForUser = useCallback(getUpcomingEventsForUser_internal, [users, events, upcomingEventsDisplayMonths]);
  const addUserEvent = useCallback(addUserEvent_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const addAdminEvent = useCallback(addAdminEvent_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const updateAdminEvent = useCallback(updateAdminEvent_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const deleteAdminEvent = useCallback(deleteAdminEvent_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const addCategory = useCallback(addCategory_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const updateCategory = useCallback(updateCategory_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const deleteCategory = useCallback(deleteCategory_internal, [currentUser, toast, allItems, refreshAllDataFromFirestore]); // db removed
  const addGroup = useCallback(addGroup_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const updateGroup = useCallback(updateGroup_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const deleteGroup = useCallback(deleteGroup_internal, [currentUser, toast, users, events, refreshAllDataFromFirestore]); // db removed
  const addRank = useCallback(addRank_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const updateRank = useCallback(updateRank_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const deleteRank = useCallback(deleteRank_internal, [currentUser, toast, allItems, refreshAllDataFromFirestore]); // db removed
  const addUser = useCallback(addUser_internal, [currentUser, toast, refreshAllDataFromFirestore, signOutUser_internal]); // auth, db removed
  const updateUser = useCallback(updateUser_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed
  const deleteUser = useCallback(deleteUser_internal, [currentUser, users, _generateAndDispatchNotifications_internal, toast, refreshAllDataFromFirestore]); // auth, db, wishlists, allItems removed (handled by new item structure)
  const updateAnyWishlistItem = useCallback(updateAnyWishlistItem_internal, [currentUser, toast, users, _generateAndDispatchNotifications, refreshAllDataFromFirestore]); // db removed
  const deleteAnyWishlistItemByAdmin = useCallback(deleteAnyWishlistItemByAdmin_internal, [currentUser, toast, users, _generateAndDispatchNotifications, refreshAllDataFromFirestore]); // db removed
  const deleteAllWishlistItemsFromAllUsers = useCallback(deleteAllWishlistItemsFromAllUsers_internal, [currentUser, toast, refreshAllDataFromFirestore]); // db removed

  const contextValue = useMemo(() => ({
    currentUser, firebaseUser, authLoading, users, wishlists, allItems, messages, events, groupsData, categories, ranks,
    upcomingEventsDisplayMonths,
    getWishlistByUserId, getItemsForUser, getItemById,
    addItemToCurrentUserWishlist, updateItemInCurrentUserWishlist, updateAnyWishlistItem,
    deleteAnyWishlistItemByAdmin, deleteAllWishlistItemsFromAllUsers, removeItemFromCurrentUserWishlist, copyItemToCurrentUserWishlist, reserveItem, unreserveItem, purchaseItem, returnItem,
    markItemAsReceived, unmarkItemAsReceived, updateUserEmail, changeUserPassword, updateUserAllowEmailsPreference, updateUserDisplayEmailPreference, updateUserAvatar,
    getUnreadMessagesCount, getMessagesForUser, getAllMessages, deleteNotificationById, deleteAllNotificationsForUser, deleteAllNotificationsAndNotifyUsers, markMessagesAsRead,
    getUpcomingEventsForUser, addUserEvent, addAdminEvent, updateAdminEvent, deleteAdminEvent, addCategory, updateCategory, deleteCategory,
    addGroup, updateGroup, deleteGroup, addRank, updateRank, deleteRank, addUser, updateUser, deleteUser, signInUser, signOutUser,
    refreshAllDataFromFirestore,
    canViewWishlist,
  }), [
    currentUser, firebaseUser, authLoading, users, wishlists, allItems, messages, events, groupsData, categories, ranks,
    upcomingEventsDisplayMonths,
    getWishlistByUserId, getItemsForUser, getItemById,
    addItemToCurrentUserWishlist, updateItemInCurrentUserWishlist, updateAnyWishlistItem,
    deleteAnyWishlistItemByAdmin, deleteAllWishlistItemsFromAllUsers, removeItemFromCurrentUserWishlist, copyItemToCurrentUserWishlist, reserveItem, unreserveItem, purchaseItem, returnItem,
    markItemAsReceived, unmarkItemAsReceived, updateUserEmail, changeUserPassword, updateUserAllowEmailsPreference, updateUserDisplayEmailPreference, updateUserAvatar,
    getUnreadMessagesCount, getMessagesForUser, getAllMessages, deleteNotificationById, deleteAllNotificationsForUser, deleteAllNotificationsAndNotifyUsers, markMessagesAsRead,
    getUpcomingEventsForUser, addUserEvent, addAdminEvent, updateAdminEvent, deleteAdminEvent, addCategory, updateCategory, deleteCategory,
    addGroup, updateGroup, deleteGroup, addRank, updateRank, deleteRank, addUser, updateUser, deleteUser, signInUser, signOutUser,
    refreshAllDataFromFirestore,
    canViewWishlist
  ]);

  return (
    <WishlistContext.Provider value={contextValue}>
      {children}
    </WishlistContext.Provider>
  );
};

export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
