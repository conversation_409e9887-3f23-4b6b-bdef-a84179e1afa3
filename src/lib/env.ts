import { z } from 'zod';

/**
 * Environment variable validation and type safety
 */

// Simple environment variable access - just use process.env directly
export const env = {
  NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  NEXT_PUBLIC_FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  NODE_ENV: process.env.NODE_ENV || 'development',
  NEXT_PUBLIC_USE_FIREBASE_EMULATOR: process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR,
  NEXT_PUBLIC_EMULATOR_HOST: process.env.NEXT_PUBLIC_EMULATOR_HOST,
};

// Validation schema for when we need to validate
const envSchema = z.object({
  NEXT_PUBLIC_FIREBASE_API_KEY: z.string().min(1, 'Firebase API key is required'),
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: z.string().min(1, 'Firebase auth domain is required'),
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: z.string().min(1, 'Firebase project ID is required'),
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: z.string().min(1, 'Firebase storage bucket is required'),
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: z.string().min(1, 'Firebase messaging sender ID is required'),
  NEXT_PUBLIC_FIREBASE_APP_ID: z.string().min(1, 'Firebase app ID is required'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  NEXT_PUBLIC_USE_FIREBASE_EMULATOR: z.string().optional(),
  NEXT_PUBLIC_EMULATOR_HOST: z.string().optional(),
});

/**
 * Validates that all required environment variables are present and valid
 * Call this function when you need to ensure environment is properly configured
 */
export const validateEnvironment = (): boolean => {
  try {
    envSchema.parse(env);
    return true;
  } catch (error) {
    console.error('Environment validation failed:', error);
    return false;
  }
};

/**
 * Validates environment and throws if invalid (for critical operations)
 */
export const requireValidEnvironment = (): void => {
  if (!validateEnvironment()) {
    throw new Error('Invalid environment configuration - check your .env file');
  }
};

/**
 * Checks if we're in a secure environment (HTTPS in production)
 */
export const isSecureEnvironment = (): boolean => {
  if (env.NODE_ENV === 'development') return true;
  
  if (typeof window !== 'undefined') {
    return window.location.protocol === 'https:';
  }
  
  return true; // Assume secure on server-side
};

/**
 * Logs security warnings for development
 */
export const logSecurityWarnings = (): void => {
  if (env.NODE_ENV === 'development') {
    console.warn('🔒 Security Notice: Running in development mode');
    
    if (env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
      console.warn('🔥 Firebase Emulator is enabled - ensure this is disabled in production');
    }
    
    if (typeof window !== 'undefined' && window.location.protocol !== 'https:') {
      console.warn('⚠️ Running over HTTP - use HTTPS in production');
    }
  }
};