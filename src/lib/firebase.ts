
import { initializeApp, getApp, getApps } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { env, logSecurityWarnings } from './env';

// Log security warnings in development
logSecurityWarnings();

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
let app;
let db;
let authInstance;

if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp();
}

try {
  db = getFirestore(app);
  authInstance = getAuth(app);

  // Connect to emulators if running in development and specific env vars are set
  if (env.NODE_ENV === 'development') {
    if (env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
      console.log("Connecting to Firebase Emulators...");
      // Default emulator ports. Adjust if your emulator runs on different ports.
      // Make sure these ports match your firebase.json emulator configuration.
      const host = env.NEXT_PUBLIC_EMULATOR_HOST || "127.0.0.1";
      connectFirestoreEmulator(db, host, 8080);
      connectAuthEmulator(authInstance, `http://${host}:9099`, { disableWarnings: true });
      console.log(`Firestore emulator connected to ${host}:8080`);
      console.log(`Auth emulator connected to http://${host}:9099`);
    }
  }
} catch (e) {
  console.error("Error initializing Firebase services (client-side):", e);
  // db and authInstance will be undefined, context needs to handle this gracefully
}

export { app, db, authInstance as auth };
