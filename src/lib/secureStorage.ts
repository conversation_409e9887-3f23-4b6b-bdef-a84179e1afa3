/**
 * Secure storage utilities with validation and error handling
 */

interface StorageOptions {
  encrypt?: boolean;
  validate?: (value: any) => boolean;
  defaultValue?: any;
}

class SecureStorage {
  private isAvailable(): boolean {
    try {
      if (typeof window === 'undefined') return false;
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  private sanitizeKey(key: string): string {
    // Only allow alphanumeric characters, hyphens, and underscores
    return key.replace(/[^a-zA-Z0-9\-_]/g, '');
  }

  private sanitizeValue(value: string): string {
    // Basic sanitization - remove potential script tags
    return value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }

  setItem(key: string, value: any, options: StorageOptions = {}): boolean {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available');
      return false;
    }

    try {
      const sanitizedKey = this.sanitizeKey(key);
      if (!sanitizedKey) {
        console.error('Invalid storage key');
        return false;
      }

      let serializedValue: string;
      
      if (typeof value === 'string') {
        serializedValue = this.sanitizeValue(value);
      } else {
        serializedValue = JSON.stringify(value);
      }

      // Basic size check (localStorage typically has 5-10MB limit)
      if (serializedValue.length > 1024 * 1024) { // 1MB limit
        console.error('Value too large for storage');
        return false;
      }

      localStorage.setItem(sanitizedKey, serializedValue);
      return true;
    } catch (error) {
      console.error('Failed to store item:', error);
      return false;
    }
  }

  getItem<T = any>(key: string, options: StorageOptions = {}): T | null {
    if (!this.isAvailable()) {
      return options.defaultValue || null;
    }

    try {
      const sanitizedKey = this.sanitizeKey(key);
      if (!sanitizedKey) {
        return options.defaultValue || null;
      }

      const item = localStorage.getItem(sanitizedKey);
      if (item === null) {
        return options.defaultValue || null;
      }

      let parsedValue: T;
      
      try {
        parsedValue = JSON.parse(item);
      } catch {
        // If JSON parsing fails, return as string
        parsedValue = item as T;
      }

      // Validate the value if validator is provided
      if (options.validate && !options.validate(parsedValue)) {
        console.warn('Stored value failed validation, using default');
        this.removeItem(key); // Remove invalid data
        return options.defaultValue || null;
      }

      return parsedValue;
    } catch (error) {
      console.error('Failed to retrieve item:', error);
      return options.defaultValue || null;
    }
  }

  removeItem(key: string): boolean {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const sanitizedKey = this.sanitizeKey(key);
      if (!sanitizedKey) {
        return false;
      }

      localStorage.removeItem(sanitizedKey);
      return true;
    } catch (error) {
      console.error('Failed to remove item:', error);
      return false;
    }
  }

  clear(): boolean {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('Failed to clear storage:', error);
      return false;
    }
  }

  // Get storage usage information
  getStorageInfo(): { used: number; available: number; percentage: number } {
    if (!this.isAvailable()) {
      return { used: 0, available: 0, percentage: 0 };
    }

    try {
      let used = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // Estimate available space (typical localStorage limit is 5-10MB)
      const estimated_limit = 5 * 1024 * 1024; // 5MB
      const available = Math.max(0, estimated_limit - used);
      const percentage = (used / estimated_limit) * 100;

      return { used, available, percentage };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return { used: 0, available: 0, percentage: 0 };
    }
  }
}

export const secureStorage = new SecureStorage();

// Theme-specific storage with validation
export const themeStorage = {
  get: (): string | null => {
    return secureStorage.getItem('giftlink-theme', {
      validate: (value) => ['light', 'dark', 'high-contrast', 'christmas', 'fourth-of-july', 'easter'].includes(value),
      defaultValue: null
    });
  },
  
  set: (theme: string): boolean => {
    const validThemes = ['light', 'dark', 'high-contrast', 'christmas', 'fourth-of-july', 'easter'];
    if (!validThemes.includes(theme)) {
      console.error('Invalid theme value');
      return false;
    }
    return secureStorage.setItem('giftlink-theme', theme);
  },
  
  remove: (): boolean => {
    return secureStorage.removeItem('giftlink-theme');
  }
};