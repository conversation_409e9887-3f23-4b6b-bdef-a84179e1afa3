
export interface Commitment {
  id: string; // Unique ID for the commitment itself
  commitmentId?: string; // Keep for backward compatibility if needed from old data, but prefer id
  committedByUid: string; // Firebase Auth UID
  quantity: number;
  type: 'reserved' | 'purchased';
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface WishlistItem {
  id: string; // Firestore Document ID for the item
  uniqueId: string; // Auto-generated 20-character alphanumeric identifier
  ownerUid: string; // Firebase Auth UID of the owner
  wishlistId: string; // ID of the wishlist this item belongs to (currently same as ownerUid)
  name: string;
  price: number;
  retailer: string;
  url?: string;
  imageUrl?: string;
  description?: string;
  categoryId?: string; // ID of the category from 'categories' collection
  rankId?: string;     // ID of the rank from 'ranks' collection
  quantity: number;
  receivedQuantity: number;
  commitments: Commitment[];
  dateAdded: string; // ISO date string (or Firestore Timestamp for DB)
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface User {
  id: string; // This is the Firebase Auth UID, and also the Firestore Document ID
  name: string;
  email: string; // Should match Firebase Auth email
  avatarUrl?: string;
  allowEmails: boolean;
  displayEmailToGroupMembers: boolean;
  isApproved: boolean;
  isAdmin: boolean;
  groups: string[]; // Array of group IDs (Document IDs from 'groups' collection)
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface Group {
  id: string; // Firestore Document ID
  name: string;
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface Wishlist { // Represents a user's wishlist document in Firestore
  id: string; // Document ID (same as userId / Firebase Auth UID)
  userId: string; // Firebase Auth UID
  // items: WishlistItem[]; // REMOVED - Items are now in a separate collection
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface Message {
  id: string; // Firestore Document ID
  recipientUid: string; // Firebase Auth UID
  actingUid?: string; // Firebase Auth UID
  actingUserName?: string;
  eventType:
    | 'item_added'
    | 'item_updated'
    | 'item_deleted_by_owner'
    | 'item_commitment_impacted_by_deletion'
    | 'item_updated_by_admin'
    | 'item_deleted_by_admin'
    | 'system_notification_reset';
  itemInfo?: {
    id?: string; // Item ID
    name: string;
    oldName?: string;
    ownerName?: string;
  };
  commitmentInfo?: {
    type: 'reserved' | 'purchased';
    quantity: number;
  };
  adminReason?: string;
  changes?: { field: string; oldValue: string | number | null; newValue: string | number | null }[];
  content: string;
  timestamp?: string; // ISO date string (or Firestore Timestamp for DB)
  isRead: boolean;
  updatedAt?: string; // ISO date string
}

export interface Event {
  id: string; // Firestore Document ID
  name: string;
  description: string;
  date: string; // "MM-DD" for recurring, "YYYY-MM-DD" for non-recurring
  isRecurringYearly: boolean;
  type: 'system' | 'user';
  createdByUserId: string; // "system" or a Firebase Auth UID
  associatedGroupIds: string[]; // Array of Group IDs
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface UpcomingEventDisplayInfo {
  id: string;
  name: string;
  description: string;
  displayDate: Date; // Actual date of next occurrence
  isRecurringYearly: boolean;
  type: 'system' | 'user';
  originalEventDate: string; // The "MM-DD" or "YYYY-MM-DD" string from DB
  associatedGroupIds: string[];
  createdByUserId: string;
}

export interface Category {
  id: string; // Firestore Document ID
  title: string;
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}

export interface Rank {
  id: string; // Firestore Document ID
  title: string;
  description?: string;
  order: number;
  createdAt?: string; // ISO date string (or Firestore Timestamp for DB)
  updatedAt?: string; // ISO date string (or Firestore Timestamp for DB)
}
