import { z } from 'zod';

/**
 * Security-focused validation schemas and utilities
 */

// Common validation patterns
const SAFE_STRING_REGEX = /^[a-zA-Z0-9\s\-_.,!?()'"]+$/;
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Input sanitization functions
export const sanitizeString = (input: string, maxLength: number = 1000): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .slice(0, maxLength)
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/vbscript:/gi, ''); // Remove vbscript: protocol
};

export const sanitizeUrl = (url: string): string => {
  if (typeof url !== 'string') return '';
  
  const sanitized = url.trim();
  
  // Only allow http/https protocols
  if (!URL_REGEX.test(sanitized)) {
    return '';
  }
  
  // Block dangerous protocols
  if (sanitized.toLowerCase().includes('javascript:') || 
      sanitized.toLowerCase().includes('data:') ||
      sanitized.toLowerCase().includes('vbscript:')) {
    return '';
  }
  
  return sanitized;
};

export const sanitizeEmail = (email: string): string => {
  if (typeof email !== 'string') return '';
  
  const sanitized = email.trim().toLowerCase();
  return EMAIL_REGEX.test(sanitized) ? sanitized : '';
};

// Enhanced Zod schemas with security validations
export const secureStringSchema = (maxLength: number = 1000) => 
  z.string()
    .max(maxLength, `Input too long (max ${maxLength} characters)`)
    .refine(
      (val) => !val.includes('<script>') && !val.includes('javascript:'),
      'Invalid characters detected'
    )
    .transform(val => sanitizeString(val, maxLength));

export const secureUrlSchema = z.string()
  .url('Invalid URL format')
  .refine(
    (url) => url.startsWith('http://') || url.startsWith('https://'),
    'Only HTTP/HTTPS URLs are allowed'
  )
  .transform(sanitizeUrl);

export const secureEmailSchema = z.string()
  .email('Invalid email format')
  .transform(sanitizeEmail);

// File upload validation
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.' };
  }
  
  if (file.size > maxSize) {
    return { valid: false, error: 'File too large. Maximum size is 5MB.' };
  }
  
  // Check for suspicious file names
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    return { valid: false, error: 'Invalid file name.' };
  }
  
  return { valid: true };
};

// Rate limiting helper (client-side basic implementation)
class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  
  isAllowed(key: string, maxAttempts: number = 5, windowMs: number = 60000): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return false;
    }
    
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    return true;
  }
  
  reset(key: string): void {
    this.attempts.delete(key);
  }
}

export const rateLimiter = new RateLimiter();

// Content Security Policy helpers
export const generateNonce = (): string => {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

// Secure random ID generation (better than the current implementation)
export const generateSecureId = (length: number = 20): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => chars[byte % chars.length]).join('');
};