
import { Timestamp } from 'firebase/firestore';
import { isValid, isDate } from 'date-fns';
import type { WishlistItem, Commitment, AppUser, Category, Rank, Group, Event, Message, Wishlist } from './types';
import { v4 as uuidv4 } from 'uuid';
import { toast } from '@/hooks/use-toast';
import type { DocumentSnapshot } from 'firebase/firestore';

export const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) return error.message;
  return String(error);
};

// Standardized error handling utility for service functions
interface ServiceErrorOptions {
  showToast?: boolean;
  toastTitle?: string;
  toastDuration?: number;
  returnValue?: any;
}

export const handleServiceError = (
  error: unknown, 
  operation: string,
  options?: ServiceErrorOptions
) => {
  const {
    showToast = true,
    toastTitle = "Operation Failed",
    toastDuration = 5000,
    returnValue = null
  } = options || {};
  
  console.error(`Error ${operation}:`, error);
  
  if (showToast) {
    toast({
      title: toastTitle,
      description: `Could not ${operation}. ${getErrorMessage(error)}`,
      variant: "destructive",
      duration: toastDuration
    });
  }
  
  return returnValue;
};

// Firestore document mapper functions to eliminate code duplication
// These functions provide consistent transformation from Firestore documents to application models

export const mapFirestoreDocToCategory = (doc: DocumentSnapshot): Category => ({
  ...doc.data(),
  id: doc.id,
  createdAt: formatTimestamp(doc.data()?.createdAt),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as Category);

export const mapFirestoreDocToRank = (doc: DocumentSnapshot): Rank => ({
  ...doc.data(),
  id: doc.id,
  createdAt: formatTimestamp(doc.data()?.createdAt),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as Rank);

export const mapFirestoreDocToUser = (doc: DocumentSnapshot): AppUser => ({
  ...doc.data(),
  id: doc.id,
  createdAt: formatTimestamp(doc.data()?.createdAt),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as AppUser);

export const mapFirestoreDocToGroup = (doc: DocumentSnapshot): Group => ({
  ...doc.data() as Omit<Group, 'id'>,
  id: doc.id,
  createdAt: formatTimestamp(doc.data()?.createdAt),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as Group);

export const mapFirestoreDocToEvent = (doc: DocumentSnapshot): Event => ({
  ...doc.data(),
  id: doc.id,
  date: String(doc.data()?.date),
  createdAt: formatTimestamp(doc.data()?.createdAt),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as Event);

export const mapFirestoreDocToWishlistShell = (doc: DocumentSnapshot): Wishlist => ({
  id: doc.id,
  userId: doc.data()?.userId || doc.id,
  createdAt: formatTimestamp(doc.data()?.createdAt),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as Wishlist);

export const mapFirestoreDocToMessage = (doc: DocumentSnapshot): Message => ({
  ...doc.data(),
  id: doc.id,
  timestamp: formatTimestamp(doc.data()?.timestamp),
  updatedAt: formatTimestamp(doc.data()?.updatedAt)
} as Message);

// Helper function to map commitment data
export const mapCommitmentData = (c: any): Commitment => ({
  ...c,
  id: c.id || c.commitmentId || uuidv4(),
  createdAt: formatTimestamp(c.createdAt)
});

export const mapFirestoreDocToWishlistItem = (doc: DocumentSnapshot): WishlistItem => {
  const data = doc.data();
  return {
    ...data,
    id: doc.id,
    dateAdded: formatTimestamp(data?.dateAdded),
    commitments: Array.isArray(data?.commitments) 
      ? data.commitments.map(mapCommitmentData)
      : [],
    receivedQuantity: data?.receivedQuantity || 0,
    createdAt: formatTimestamp(data?.createdAt),
    updatedAt: formatTimestamp(data?.updatedAt)
  } as WishlistItem;
};

export const formatTimestamp = (timestampInput: any): string | undefined => {
    if (!timestampInput) return undefined;
    let date: Date | null = null;
    if (timestampInput instanceof Timestamp) {
        date = timestampInput.toDate();
    } else if (typeof timestampInput === 'string' || typeof timestampInput === 'number') {
        try { const parsed = new Date(timestampInput); if (isValid(parsed)) date = parsed; } catch (e) { /* ignore */ }
    } else if (timestampInput && typeof timestampInput.seconds === 'number' && typeof timestampInput.nanoseconds === 'number') {
        try { const parsed = new Date(timestampInput.seconds * 1000 + timestampInput.nanoseconds / 1000000); if (isValid(parsed)) date = parsed; } catch (e) { /* ignore */ }
    } else if (isDate(timestampInput) && isValid(timestampInput)) {
        date = timestampInput;
    }
    return date && isValid(date) ? date.toISOString() : undefined;
};

export const getTotalCommittedQuantity = (item: WishlistItem, type?: 'reserved' | 'purchased'): number => {
    if (!item || !Array.isArray(item.commitments)) return 0;
    return item.commitments.reduce((sum, c) => (!type || c.type === type) ? sum + (c.quantity || 0) : sum, 0);
};

export const getUserCommittedQuantity = (item: WishlistItem, userId: string, type?: 'reserved' | 'purchased'): number => {
    if (!item || !Array.isArray(item.commitments) || !userId) return 0;
    return item.commitments.reduce((sum, c) => (c.committedByUid === userId && (!type || c.type === type)) ? sum + (c.quantity || 0) : sum, 0);
};

export const getAvailableQuantity = (item: WishlistItem): number => {
    if (!item) return 0;
    const totalDesired = item.quantity || 0;
    const totalPurchased = getTotalCommittedQuantity(item, 'purchased');
    const totalReceivedByOwner = item.receivedQuantity || 0;
    return Math.max(0, totalDesired - totalPurchased - totalReceivedByOwner);
};

export const getReservableQuantity = (item: WishlistItem, currentUserId: string | null): number => {
    if (!item || !currentUserId) return 0;
    const totalDesired = item.quantity || 0;
    const totalPurchasedByAnyone = getTotalCommittedQuantity(item, 'purchased');
    const totalReceivedByOwner = item.receivedQuantity || 0;
    const commitments = Array.isArray(item.commitments) ? item.commitments : [];
    const totalReservedByOthers = commitments.filter(c => c.type === 'reserved' && c.committedByUid !== currentUserId).reduce((sum, c) => sum + (c.quantity || 0), 0);
    const neededAndNotPurchasedOrReceived = Math.max(0, totalDesired - totalPurchasedByAnyone - totalReceivedByOwner);
    return Math.max(0, neededAndNotPurchasedOrReceived - totalReservedByOthers);
};

export const getDirectlyPurchasableQuantity = (item: WishlistItem, currentUserId: string | null): number => {
    if (!item || !currentUserId) return 0;
    const totalDesired = item.quantity || 0;
    const totalPurchasedOverall = getTotalCommittedQuantity(item, 'purchased');
    const totalReceivedByOwner = item.receivedQuantity || 0;

    const stillNeededByOwner = Math.max(0, totalDesired - totalPurchasedOverall - totalReceivedByOwner);
    if (stillNeededByOwner === 0) return 0;

    const commitments = Array.isArray(item.commitments) ? item.commitments : [];
    const reservedByOthers = commitments
      .filter(c => c.type === 'reserved' && c.committedByUid !== currentUserId)
      .reduce((sum, c) => sum + (c.quantity || 0), 0);

    return Math.max(0, stillNeededByOwner - reservedByOthers);
};

export const createNewCommitment = (committedByUid: string, quantity: number, type: 'reserved' | 'purchased'): Commitment => {
  return {
    id: uuidv4(),
    committedByUid,
    quantity,
    type,
    createdAt: new Date().toISOString(), // Client-side timestamp, Firestore will use serverTimestamp
  };
};
