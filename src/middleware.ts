import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();

  // Security headers for all routes
  response.headers.set('X-DNS-Prefetch-Control', 'off');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Remove server information
  response.headers.delete('Server');
  response.headers.delete('X-Powered-By');

  // HSTS for production HTTPS
  if (process.env.NODE_ENV === 'production') {
    response.headers.set(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains; preload'
    );
  }

  // Rate limiting headers (basic implementation)
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = request.headers.get('user-agent') || '';
  
  // Block suspicious user agents
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /php/i,
    /java/i,
    /go-http-client/i,
    /okhttp/i,
    /apache-httpclient/i,
  ];

  // Allow legitimate bots but block others
  const legitimateBots = [
    /googlebot/i,
    /bingbot/i,
    /slurp/i,
    /duckduckbot/i,
    /baiduspider/i,
    /yandexbot/i,
    /facebookexternalhit/i,
    /twitterbot/i,
    /linkedinbot/i,
    /whatsapp/i,
    /telegrambot/i,
  ];

  const isLegitimateBot = legitimateBots.some(pattern => pattern.test(userAgent));
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));

  if (isSuspicious && !isLegitimateBot) {
    console.warn(`Blocked suspicious user agent: ${userAgent} from IP: ${ip}`);
    return new NextResponse('Access Denied', { status: 403 });
  }

  // Block requests with suspicious paths
  const suspiciousPaths = [
    '/.env',
    '/wp-admin',
    '/wp-login',
    '/admin.php',
    '/phpmyadmin',
    '/config.php',
    '/.git',
    '/backup',
    '/database',
    '/sql',
    '/shell',
    '/cmd',
    '/eval',
    '/system',
    '/proc',
    '/etc/passwd',
    '/etc/shadow',
  ];

  if (suspiciousPaths.some(path => request.nextUrl.pathname.includes(path))) {
    console.warn(`Blocked suspicious path: ${request.nextUrl.pathname} from IP: ${ip}`);
    return new NextResponse('Not Found', { status: 404 });
  }

  // Block requests with suspicious query parameters
  const suspiciousParams = ['eval', 'exec', 'system', 'shell', 'cmd', 'script', 'javascript'];
  const searchParams = request.nextUrl.searchParams;
  
  for (const [key, value] of searchParams.entries()) {
    if (suspiciousParams.some(param => 
      key.toLowerCase().includes(param) || 
      value.toLowerCase().includes(param)
    )) {
      console.warn(`Blocked suspicious query parameter: ${key}=${value} from IP: ${ip}`);
      return new NextResponse('Bad Request', { status: 400 });
    }
  }

  // Basic rate limiting for login attempts
  if (request.nextUrl.pathname === '/login' && request.method === 'POST') {
    // In a real application, you'd use Redis or a database for this
    // This is a basic in-memory implementation for demonstration
    response.headers.set('X-RateLimit-Limit', '5');
    response.headers.set('X-RateLimit-Remaining', '4');
    response.headers.set('X-RateLimit-Reset', String(Date.now() + 300000)); // 5 minutes
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};