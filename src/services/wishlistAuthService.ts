
import { type User as FirebaseUser, signInWithEmailAndPassword, signOut, updatePassword as firebaseUpdatePassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc, writeBatch, serverTimestamp, collection } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import type { User as AppUser } from '@/lib/types';
import { toast } from '@/hooks/use-toast';
import { getErrorMessage } from '@/lib/wishlistUtils';
import { rateLimiter, secureEmailSchema } from '@/lib/validation';

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const signInUser = async (email: string, pass: string): Promise<{success: boolean, firebaseUser?: FirebaseUser | null}> => {
  if (!auth) {
    toast({ title: "Login Error", description: "Authentication service not ready.", variant: "destructive" });
    return {success: false};
  }

  // Rate limiting for login attempts
  const clientId = `login_${email}`;
  if (!rateLimiter.isAllowed(clientId, 5, 300000)) { // 5 attempts per 5 minutes
    toast({ 
      title: "Too Many Attempts", 
      description: "Please wait before trying again.", 
      variant: "destructive" 
    });
    return {success: false};
  }

  // Validate and sanitize email
  try {
    secureEmailSchema.parse(email);
  } catch {
    toast({ title: "Invalid Input", description: "Please enter a valid email address.", variant: "destructive" });
    return {success: false};
  }

  // Basic password validation
  if (!pass || pass.length < 6) {
    toast({ title: "Invalid Input", description: "Password must be at least 6 characters.", variant: "destructive" });
    return {success: false};
  }

  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, pass);
    rateLimiter.reset(clientId); // Reset rate limit on successful login
    toast({ title: "Login Successful", description: "Welcome back!" });
    return {success: true, firebaseUser: userCredential.user};
  } catch (error: any) {
    console.error("Firebase Auth Error:", error);
    let errorMessage = "Login failed. Please check your credentials.";
    if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential') {
      errorMessage = "Invalid email or password.";
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = "Please enter a valid email address.";
    } else if (error.code === 'auth/too-many-requests') {
      errorMessage = "Access to this account has been temporarily disabled due to many failed login attempts. You can try again later or reset your password.";
    }
    toast({ title: "Login Failed", description: errorMessage, variant: "destructive" });
    return {success: false};
  }
};

export const signOutUser = async (): Promise<void> => {
  if (!auth) {
    console.error("Firebase auth instance not available for sign out.");
    return;
  }
  try {
    await signOut(auth);
    toast({ title: "Logged Out", description: "You have been successfully logged out." });
  } catch (error) {
    console.error("Error signing out from Firebase:", error);
    toast({ title: "Logout Failed", description: "Could not sign out.", variant: "destructive" });
  }
};

export const changeUserPassword = async (newPassword: string, currentFbUser: FirebaseUser | null): Promise<boolean> => {
  if (!currentFbUser || !auth) {
    toast({ title: "Error", description: "Not logged in.", variant: "destructive" });
    return false;
  }
  try {
    await firebaseUpdatePassword(currentFbUser, newPassword);
    toast({ title: "Password Updated", description: "Your GiftLink account password has been successfully updated." });
    return true;
  } catch (error: any) {
    console.error("Firebase Change Password Error:", error);
    let desc = "Could not update password.";
    if (error.code === 'auth/requires-recent-login') {
      desc = "This operation is sensitive and requires recent authentication. Please log out and log back in to continue.";
    } else if (error.code === 'auth/weak-password') {
      desc = "Password is too weak (min 6 characters for Firebase).";
    }
    toast({ title: "Password Change Failed", description: desc, variant: "destructive" });
    return false;
  }
};

export const addAuthUserAndProfile = async (
  userData: Omit<AppUser, 'id' | 'avatarUrl' | 'createdAt' | 'updatedAt'> & { password?: string },
  currentAdminUser: AppUser | null
): Promise<{ success: boolean; newUserId?: string; newFirebaseUser?: FirebaseUser | null }> => {
  if (!currentAdminUser?.isAdmin || !auth || !db) {
    toast({ title: "Permission Denied", description: "Admin only.", variant: "destructive" });
    return { success: false };
  }
  if (!userData.password) {
    toast({ title: "Error", description: "Password required for new user.", variant: "destructive" });
    return { success: false };
  }

  // Store current admin credentials before we get logged out
  const adminEmail = currentAdminUser.email;
  // We need to prompt for admin password since we can't store it
  const adminPassword = prompt("Please enter your admin password to continue after user creation:");
  
  if (!adminPassword) {
    toast({ 
      title: "Operation Cancelled", 
      description: "Admin password is required to complete this operation.", 
      variant: "destructive" 
    });
    return { success: false };
  }

  let newFirebaseUser: FirebaseUser | null = null;
  try {
    // Create the user - this will log out the admin and log in as the new user
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
    newFirebaseUser = userCredential.user;
    const newUserId = newFirebaseUser.uid;

    const { password, ...profileDataToSave } = userData;
    const userProfileForFirestore: Omit<AppUser, 'id'> = {
      ...profileDataToSave,
      avatarUrl: profileDataToSave.avatarUrl || "",
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any,
    };

    // Create user documents in Firestore
    try {
      const batch = writeBatch(db);
      batch.set(doc(db, "users", newUserId), userProfileForFirestore);
      batch.set(doc(db, "wishlists", newUserId), {
        userId: newUserId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      await batch.commit();
      
      // Success! Now sign out the new user
      await signOut(auth);
      
      // Sign back in as admin
      try {
        await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
        
        toast({ 
          title: "User Created", 
          description: `User ${userData.name} created successfully. You've been signed back in as admin.`, 
          duration: 5000 
        });
        return { success: true, newUserId };
      } catch (signInError: any) {
        toast({ 
          title: "Admin Sign-in Failed", 
          description: "User was created successfully, but we couldn't sign you back in as admin. Please sign in manually.",
          variant: "destructive",
          duration: 10000 
        });
        return { success: true, newUserId }; // Still return success for the user creation
      }
    } catch (firestoreError) {
      // Firestore write failed, but Auth user was created
      // Sign out the new user
      await signOut(auth);
      
      // Try to sign back in as admin
      try {
        await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
      } catch (signInError: any) {
        toast({ 
          title: "Admin Sign-in Failed", 
          description: "Please sign in manually as admin.",
          variant: "destructive",
          duration: 7000 
        });
      }
      
      toast({ 
        title: "User Creation Failed", 
        description: "User was created in Auth but not in Firestore. Please check your security rules.",
        variant: "destructive",
        duration: 7000
      });
      
      return { success: false };
    }
  } catch (error: any) {
    let desc = getErrorMessage(error);
    if (error.code === 'auth/email-already-in-use') desc = "Email already in use in Firebase Auth.";
    else if (error.code === 'auth/weak-password') desc = "Password is too weak (min 6 characters for Firebase).";
    toast({ title: "User Creation Failed", description: desc, variant: "destructive" });
    return { success: false };
  }
};