
import { db } from '@/lib/firebase';
import { toast } from '@/hooks/use-toast';
import { collection, addDoc, updateDoc, deleteDoc, doc, serverTimestamp, type FieldValue } from 'firebase/firestore';
import type { AppUser, Event, UpcomingEventDisplayInfo, Group } from '@/lib/types';
import { addMonths, format, getYear, parse, isWithinInterval, setYear, startOfDay, isValid, differenceInDays } from 'date-fns';

export const getUpcomingEventsForUser = (
  userId: string,
  allUsers: AppUser[],
  allEvents: Event[],
  upcomingEventsDisplayMonths: number
): UpcomingEventDisplayInfo[] => {
  const userProfile = allUsers.find(u => u.id === userId);
  if (!userProfile) return [];

  const userGroupIds = userProfile.groups || [];
  const now = startOfDay(new Date());
  const endDateLimit = addMonths(now, upcomingEventsDisplayMonths);
  const currentYear = getYear(now);

  return allEvents
    .filter(event => {
      if (!event.type || !event.date) return false;
      if (event.type === 'system') return true;
      if (event.type === 'user' && userGroupIds.some(ugid => (event.associatedGroupIds || []).includes(ugid))) return true;
      return false;
    })
    .map(event => {
      let displayDate: Date | null = null;
      try {
        if (event.isRecurringYearly) {
          const [monthStr, dayStr] = event.date.split('-');
          const month = parseInt(monthStr, 10) - 1;
          const day = parseInt(dayStr, 10);
          if (isNaN(month) || isNaN(day) || month < 0 || month > 11 || day < 1 || day > 31) {
            throw new Error("Invalid recurring date format: " + event.date);
          }
          let nextOccurrenceThisYear = setYear(new Date(currentYear, month, day), currentYear);
          if (nextOccurrenceThisYear < now && differenceInDays(now, nextOccurrenceThisYear) > 0) {
            displayDate = setYear(nextOccurrenceThisYear, currentYear + 1);
          } else {
            displayDate = nextOccurrenceThisYear;
          }
        } else {
          const parsedNonRecurring = parse(event.date, 'yyyy-MM-dd', new Date());
          if (isValid(parsedNonRecurring)) {
            displayDate = parsedNonRecurring;
          } else {
            throw new Error("Invalid non-recurring date format: " + event.date);
          }
        }
      } catch (e: any) {
        console.error(`Error parsing event date for event "${event.name}" (ID: ${event.id}, Date string: "${event.date}"):`, e.message);
        return null;
      }
      return displayDate && isValid(displayDate) ? { ...event, displayDate, originalEventDate: event.date } : null;
    })
    .filter(eventInfo => eventInfo && eventInfo.displayDate && isWithinInterval(eventInfo.displayDate, { start: now, end: endDateLimit }))
    .sort((a, b) => a!.displayDate.getTime() - b!.displayDate.getTime()) as UpcomingEventDisplayInfo[];
};

export const addUserEvent = async (
  eventData: Omit<Event, 'id' | 'type' | 'createdAt' | 'updatedAt' | 'createdByUserId'>,
  currentUser: AppUser | null
): Promise<Event | null> => {
  if (!currentUser || !currentUser.isApproved || !db) return null;
  const dataToAdd: Omit<Event, 'id'> = {
    ...eventData,
    type: 'user',
    createdByUserId: currentUser.id,
    createdAt: serverTimestamp() as any,
    updatedAt: serverTimestamp() as any,
  };
  try {
    const docRef = await addDoc(collection(db, "events"), dataToAdd);
    return { ...dataToAdd, id: docRef.id, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() } as Event;
  } catch (error) {
    console.error("Error adding user event:", error);
    toast({ title: "Event Error", description: "Could not add event.", variant: "destructive" });
    return null;
  }
};

export const addAdminEvent = async (
  eventData: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>,
  currentAdminUser: AppUser | null
): Promise<Event | null> => {
  if (!currentAdminUser?.isAdmin || !db) return null;
  const dataToAdd: Omit<Event, 'id'> = {
    ...eventData,
    createdAt: serverTimestamp() as any,
    updatedAt: serverTimestamp() as any
  };
  try {
    const docRef = await addDoc(collection(db, "events"), dataToAdd);
    return { ...dataToAdd, id: docRef.id, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() } as Event;
  } catch (error) {
    console.error("Error adding admin event:", error);
    toast({ title: "Event Error", description: "Could not add event.", variant: "destructive" });
    return null;
  }
};

export const updateAdminEvent = async (
  eventId: string,
  eventData: Partial<Omit<Event, 'id' | 'createdAt' | 'updatedAt'>>,
  currentAdminUser: AppUser | null
): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  try {
    const dataToUpdate: any = { ...eventData, updatedAt: serverTimestamp() };
    await updateDoc(doc(db, "events", eventId), dataToUpdate);
    return true;
  } catch (error) {
    console.error("Error updating admin event:", error);
    toast({ title: "Event Error", description: "Could not update event.", variant: "destructive" });
    return false;
  }
};

export const deleteAdminEvent = async (eventId: string, currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  try {
    await deleteDoc(doc(db, "events", eventId));
    return true;
  } catch (error) {
    console.error("Error deleting admin event:", error);
    toast({ title: "Event Error", description: "Could not delete event.", variant: "destructive" });
    return false;
  }
};
