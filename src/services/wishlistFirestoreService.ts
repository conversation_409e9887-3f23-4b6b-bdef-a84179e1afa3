import { db } from '@/lib/firebase';
import { toast } from '@/hooks/use-toast';
import {
  doc, getDoc, collection, getDocs, setDoc, addDoc, updateDoc, deleteDoc,
  writeBatch, query, where, orderBy, serverTimestamp, type FieldValue,
  limit, documentId
} from 'firebase/firestore';
import type { AppUser, WishlistItem, Wishlist, Commitment, Message, Event, Group, Category, Rank } from '@/lib/types';
import { 
  formatTimestamp, 
  getErrorMessage, 
  handleServiceError,
  mapFirestoreDocToCategory,
  mapFirestoreDocToRank,
  mapFirestoreDocToUser,
  mapFirestoreDocToGroup,
  mapFirestoreDocToEvent,
  mapFirestoreDocToWishlistShell,
  mapFirestoreDocToMessage,
  mapFirestoreDocToWishlistItem
} from '@/lib/wishlistUtils';
// Notification generation will be handled by the WishlistContext after these service functions return.
import type { User as FirebaseUser } from 'firebase/auth';
import { v4 as uuidv4 } from 'uuid';

import { generateSecureId } from '@/lib/validation';

const generateUniqueId = (): string => {
  return generateSecureId(20);
};

interface FetchAllDataResult {
  users: AppUser[];
  groupsData: Group[];
  events: Event[];
  categories: Category[];
  ranks: Rank[];
  wishlistShells: Wishlist[];
  allItems: WishlistItem[];
  messages: Message[];
}

// Helper functions for fetching specific data types
const fetchCategories = async (currentFbUser: FirebaseUser | null): Promise<Category[]> => {
  if (!db) return [];
  try {
    const categoriesSnap = await getDocs(query(collection(db, "categories"), orderBy("title")));
    return categoriesSnap.docs.map(mapFirestoreDocToCategory);
  } catch (error) {
    return handleServiceError(error, "fetch categories", {
      toastTitle: "Data Load Error",
      showToast: !!currentFbUser,
      returnValue: []
    });
  }
};

const fetchRanks = async (currentFbUser: FirebaseUser | null): Promise<Rank[]> => {
  if (!db) return [];
  try {
    const ranksSnap = await getDocs(query(collection(db, "ranks"), orderBy("order")));
    return ranksSnap.docs.map(mapFirestoreDocToRank);
  } catch (error) {
    return handleServiceError(error, "fetch ranks", {
      toastTitle: "Data Load Error",
      showToast: !!currentFbUser,
      returnValue: []
    });
  }
};

const fetchAdminUsers = async (): Promise<AppUser[]> => {
  if (!db) return [];
  try {
    const usersSnap = await getDocs(query(collection(db, "users"), orderBy("name")));
    return usersSnap.docs.map(mapFirestoreDocToUser);
  } catch (error) {
    return handleServiceError(error, "fetch users", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchRegularUsers = async (currentAppUser: AppUser, currentFbUser: FirebaseUser): Promise<AppUser[]> => {
  if (!db) return [currentAppUser];
  try {
    const users = [currentAppUser]; // Start with current user
    const otherUsersQuery = query(collection(db, "users"), where("isApproved", "==", true));
    const otherUsersSnap = await getDocs(otherUsersQuery);
    
    otherUsersSnap.forEach(userDoc => {
      if (userDoc.id !== currentFbUser.uid) {
        users.push(mapFirestoreDocToUser(userDoc));
      }
    });
    
    // CRITICAL: Filter to only users the current user can actually view (shares groups with)
    // Firestore security rules are AUTHORIZATION, not FILTERS. If we query for documents
    // the user isn't authorized to read, the entire query will FAIL, not just filter out
    // unauthorized documents. We must pre-filter to only authorized users.
    const currentUserGroups = currentAppUser.groups || [];
    console.log(`[DEBUG] Current user ${currentAppUser.id} (${currentAppUser.name}) has groups:`, currentUserGroups);
    
    const accessibleUsers = users.filter(user => {
      if (user.id === currentAppUser.id) {
        console.log(`[DEBUG] Including self: ${user.id} (${user.name})`);
        return true; // Always include self
      }
      if (!user.isApproved) {
        console.log(`[DEBUG] Excluding unapproved user: ${user.id} (${user.name})`);
        return false; // Only approved users
      }
      const userGroups = user.groups || [];
      const sharesGroup = currentUserGroups.some(g => userGroups.includes(g));
      console.log(`[DEBUG] User ${user.id} (${user.name}) has groups:`, userGroups, `- shares group: ${sharesGroup}`);
      return sharesGroup; // Must share at least one group
    });
    
    console.log(`[DEBUG] Filtered ${users.length} users down to ${accessibleUsers.length} accessible users`);
    return accessibleUsers;
  } catch (error) {
    return handleServiceError(error, "fetch users", {
      toastTitle: "Data Load Error",
      returnValue: [currentAppUser]
    });
  }
};

const fetchAdminGroups = async (): Promise<Group[]> => {
  if (!db) return [];
  try {
    const groupsSnap = await getDocs(query(collection(db, "groups"), orderBy("name")));
    return groupsSnap.docs.map(mapFirestoreDocToGroup);
  } catch (error) {
    return handleServiceError(error, "fetch groups", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchUserGroups = async (groupIds: string[]): Promise<Group[]> => {
  if (!db || !groupIds.length) return [];
  try {
    const groupsQuery = query(collection(db, "groups"), where(documentId(), "in", groupIds.slice(0, 30)));
    const groupsSnap = await getDocs(groupsQuery);
    return groupsSnap.docs.map(mapFirestoreDocToGroup);
  } catch (error) {
    return handleServiceError(error, "fetch groups", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchAdminEvents = async (): Promise<Event[]> => {
  if (!db) return [];
  try {
    const eventsSnap = await getDocs(query(collection(db, "events"), orderBy("date", "asc")));
    return eventsSnap.docs.map(mapFirestoreDocToEvent);
  } catch (error) {
    return handleServiceError(error, "fetch events", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchUserEvents = async (groupIds: string[]): Promise<Event[]> => {
  if (!db) return [];
  try {
    const relevantEvents: Event[] = [];
    
    // Fetch system events
    const systemEventsQuery = query(collection(db, "events"), where("type", "==", "system"));
    const systemEventsSnap = await getDocs(systemEventsQuery);
    systemEventsSnap.forEach(doc => relevantEvents.push(mapFirestoreDocToEvent(doc)));

    // Fetch user group events if user has groups
    if (groupIds.length > 0) {
      const userGroupEventsQuery = query(
        collection(db, "events"), 
        where("type", "==", "user"), 
        where("associatedGroupIds", "array-contains-any", groupIds.slice(0, 30))
      );
      const userGroupEventsSnap = await getDocs(userGroupEventsQuery);
      userGroupEventsSnap.forEach(doc => {
        if (!relevantEvents.find(e => e.id === doc.id)) {
          relevantEvents.push(mapFirestoreDocToEvent(doc));
        }
      });
    }
    
    return relevantEvents.sort((a, b) => (a.date && b.date) ? a.date.localeCompare(b.date) : 0);
  } catch (error) {
    return handleServiceError(error, "fetch events", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchAdminWishlistShells = async (): Promise<Wishlist[]> => {
  if (!db) return [];
  try {
    const allWishlistShellsSnap = await getDocs(collection(db, "wishlists"));
    return allWishlistShellsSnap.docs.map(mapFirestoreDocToWishlistShell);
  } catch (error) {
    return handleServiceError(error, "fetch wishlist shells", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchUserWishlistShells = async (users: AppUser[]): Promise<Wishlist[]> => {
  if (!db || !users.length) return [];
  try {
    const wishlistShells: Wishlist[] = [];
    
    console.log(`[DEBUG] Fetching wishlists for ${users.length} users:`, users.map(u => ({ id: u.id, name: u.name, groups: u.groups })));
    
    // Fetch wishlists individually to respect Firestore security rules
    // Batch queries with "in" operator fail when any document in the result set
    // would be blocked by security rules. We must fetch each user's wishlist individually.
    for (const user of users) {
      try {
        console.log(`[DEBUG] Attempting to fetch wishlist for user ${user.id} (${user.name})`);
        const wishlistDocRef = doc(db, "wishlists", user.id);
        const wishlistDoc = await getDoc(wishlistDocRef);
        
        if (wishlistDoc.exists()) {
          console.log(`[DEBUG] Successfully fetched wishlist for user ${user.id}`);
          wishlistShells.push(mapFirestoreDocToWishlistShell(wishlistDoc));
        } else {
          console.log(`[DEBUG] No wishlist document found for user ${user.id}`);
        }
      } catch (error) {
        // Individual wishlist fetch failed - likely due to security rules
        // This is expected for users we don't share groups with
        console.error(`[DEBUG] Failed to fetch wishlist for user ${user.id}:`, getErrorMessage(error));
      }
    }
    
    console.log(`[DEBUG] Successfully fetched ${wishlistShells.length} wishlists`);
    return wishlistShells;
  } catch (error) {
    return handleServiceError(error, "fetch wishlist shells", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchAdminItems = async (): Promise<WishlistItem[]> => {
  if (!db) return [];
  try {
    const allItemsSnap = await getDocs(collection(db, "items"));
    return allItemsSnap.docs.map(mapFirestoreDocToWishlistItem);
  } catch (error) {
    return handleServiceError(error, "fetch items", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchUserItems = async (users: AppUser[]): Promise<WishlistItem[]> => {
  if (!db || !users.length) return [];
  try {
    const allItems: WishlistItem[] = [];
    
    // Fetch items for each user individually to respect Firestore security rules
    // Batch queries with "in" operator fail when any document in the result set
    // would be blocked by security rules. We must query each user's items individually.
    for (const user of users) {
      try {
        const userItemsQuery = query(collection(db, "items"), where("ownerUid", "==", user.id));
        const userItemsSnap = await getDocs(userItemsQuery);
        const userItems = userItemsSnap.docs.map(mapFirestoreDocToWishlistItem);
        allItems.push(...userItems);
      } catch (error) {
        // Individual user items fetch failed - likely due to security rules
        // This is expected for users we don't share groups with
        console.log(`Could not fetch items for user ${user.id}:`, getErrorMessage(error));
      }
    }
    
    return allItems;
  } catch (error) {
    return handleServiceError(error, "fetch items", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchAdminMessages = async (): Promise<Message[]> => {
  if (!db) return [];
  try {
    const messagesCol = collection(db, "messages");
    const messagesQuery = query(messagesCol, orderBy("timestamp", "desc"), limit(200));
    const messagesSnapshot = await getDocs(messagesQuery);
    return messagesSnapshot.docs.map(mapFirestoreDocToMessage);
  } catch (error) {
    return handleServiceError(error, "fetch messages", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

const fetchUserMessages = async (userUid: string): Promise<Message[]> => {
  if (!db) return [];
  try {
    const messagesCol = collection(db, "messages");
    const userMessagesQuery = query(messagesCol, where("recipientUid", "==", userUid), orderBy("timestamp", "desc"), limit(50));
    const userMessagesSnapshot = await getDocs(userMessagesQuery);
    return userMessagesSnapshot.docs.map(mapFirestoreDocToMessage);
  } catch (error) {
    return handleServiceError(error, "fetch messages", {
      toastTitle: "Data Load Error",
      returnValue: []
    });
  }
};

// Refactored main function using the helper functions
export const fetchAllData = async (
  currentFbUser: FirebaseUser | null,
  currentAppUser: AppUser | null
): Promise<FetchAllDataResult> => {
  const result: FetchAllDataResult = {
    users: [], groupsData: [], events: [], categories: [], 
    ranks: [], wishlistShells: [], allItems: [], messages: []
  };
  
  if (!db) {
    console.error("Firestore DB not initialized for fetchAllData");
    return result;
  }
  
  // Always fetch categories and ranks
  result.categories = await fetchCategories(currentFbUser);
  result.ranks = await fetchRanks(currentFbUser);
  
  if (!currentFbUser || !currentAppUser) {
    return result;
  }
  
  try {
    // Fetch data based on user role
    if (currentAppUser.isAdmin) {
      result.users = await fetchAdminUsers();
      result.groupsData = await fetchAdminGroups();
      result.events = await fetchAdminEvents();
      result.wishlistShells = await fetchAdminWishlistShells();
      result.allItems = await fetchAdminItems();
      result.messages = await fetchAdminMessages();
    } else {
      result.users = await fetchRegularUsers(currentAppUser, currentFbUser);
      
      // Only fetch other data if we have users
      if (result.users.length > 0) {
        result.wishlistShells = await fetchUserWishlistShells(result.users);
        result.allItems = await fetchUserItems(result.users);
      }
      
      result.messages = await fetchUserMessages(currentFbUser.uid);
      
      if (currentAppUser.groups && currentAppUser.groups.length > 0) {
        result.groupsData = await fetchUserGroups(currentAppUser.groups);
        result.events = await fetchUserEvents(currentAppUser.groups);
      }
    }
  } catch (error) {
    console.error(`Error during ${currentAppUser?.isAdmin ? 'ADMIN' : 'NON-ADMIN'} data fetch:`, error);
    toast({ 
      title: "Data Load Error", 
      description: `Could not load data. ${getErrorMessage(error)}. Check console and Firestore rules.`, 
      variant: "destructive", 
      duration: 7000 
    });
  }
  
  return result;
};

export const updateUserProfileField = async (
  userId: string,
  fieldData: Partial<Omit<AppUser, 'id' | 'createdAt' | 'updatedAt' | 'password'>>,
  currentAppUser: AppUser | null
): Promise<boolean> => {
  if (!db) {
    toast({ title: "Error", description: "Database not available.", variant: "destructive" });
    return false;
  }
  if (!currentAppUser) {
      toast({ title: "Error", description: "User context not available.", variant: "destructive" });
      return false;
  }
  if (!currentAppUser.isAdmin && currentAppUser.id !== userId) {
    toast({ title: "Permission Denied", description: "You can only update your own profile.", variant: "destructive" });
    return false;
  }

  try {
    const userDocRef = doc(db, "users", userId);
    const dataToUpdate: any = { ...fieldData, updatedAt: serverTimestamp() };

    if (!currentAppUser.isAdmin) {
      delete dataToUpdate.isAdmin;
      delete dataToUpdate.isApproved;
      delete dataToUpdate.groups;
    }
    // Ensure no undefined fields are sent to Firestore
    Object.keys(dataToUpdate).forEach(key => { if (dataToUpdate[key] === undefined) delete dataToUpdate[key]; });

    await updateDoc(userDocRef, dataToUpdate);
    return true;
  } catch (error) {
    return handleServiceError(error, "update profile field", {
      toastTitle: "Update Failed",
      returnValue: false
    });
  }
};

export const getWishlistShellByUserId = async (userId: string, currentWishlistShells: Wishlist[]): Promise<Wishlist | undefined> => {
  const existingShell = currentWishlistShells.find(w => w.userId === userId);
  if (existingShell) return existingShell;

  if (db) {
    try {
      const wishlistDocRef = doc(db, "wishlists", userId);
      const wishlistDocSnap = await getDoc(wishlistDocRef);
      if (wishlistDocSnap.exists()) {
        return mapFirestoreDocToWishlistShell(wishlistDocSnap);
      }
      return undefined;
    } catch (error) {
      return handleServiceError(error, `fetch wishlist shell for user ${userId}`, {
        toastTitle: "Wishlist Load Error",
        returnValue: undefined
      });
    }
  }
  return undefined;
};

export const addItemToWishlist = async (
  itemDetails: Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>,
  currentUser: AppUser
): Promise<WishlistItem | null> => {
  if (!db) {
    toast({ title: "Error", description: "Database not available.", variant: "destructive" });
    return null;
  }
  const itemForFirestore: Omit<WishlistItem, 'id'> & { createdAt: FieldValue, updatedAt: FieldValue, dateAdded: FieldValue } = {
    uniqueId: generateUniqueId(),
    ownerUid: currentUser.id,
    wishlistId: currentUser.id,
    name: itemDetails.name,
    price: itemDetails.price,
    retailer: itemDetails.retailer,
    quantity: itemDetails.quantity || 1,
    receivedQuantity: 0,
    commitments: [], // Commitments are always initialized as an empty array for new items
    dateAdded: serverTimestamp(),
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    url: itemDetails.url, 
    imageUrl: itemDetails.imageUrl,
    description: itemDetails.description,
    categoryId: itemDetails.categoryId,
    rankId: itemDetails.rankId,
  };
  // Ensure no undefined optional fields are sent to Firestore
  Object.keys(itemForFirestore).forEach(key => { if ((itemForFirestore as any)[key] === undefined) delete (itemForFirestore as any)[key]; });


  try {
    const newItemDocRef = await addDoc(collection(db, "items"), itemForFirestore);
    const wishlistDocRef = doc(db, "wishlists", currentUser.id);
    const wishlistSnap = await getDoc(wishlistDocRef);
    if (!wishlistSnap.exists()) {
      await setDoc(wishlistDocRef, { userId: currentUser.id, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
    } else {
      await updateDoc(wishlistDocRef, { updatedAt: serverTimestamp() });
    }
    const nowISO = new Date().toISOString();
    // Construct the item to return to the client with ISO strings for dates
    // and ensure commitments is an empty array.
    return {
      ...itemDetails, 
      id: newItemDocRef.id, 
      ownerUid: currentUser.id,
      wishlistId: currentUser.id,
      commitments: [], // Explicitly empty array
      receivedQuantity: 0,
      dateAdded: nowISO,
      createdAt: nowISO,
      updatedAt: nowISO,
      quantity: itemDetails.quantity || 1,
    } as WishlistItem;
  } catch (error: any) {
    console.error("Error adding item to 'items' collection:", error);
    toast({ title: "Add Item Failed", description: `Could not add item. ${getErrorMessage(error)}`, variant: "destructive" });
    return null;
  }
};

export const updateWishlistItem = async (
  itemId: string,
  itemDetails: Partial<Omit<WishlistItem, 'id' | 'uniqueId' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>,
  currentUser: AppUser
): Promise<WishlistItem | null> => {
  if (!db) return null;
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) {
      toast({ title: "Error", description: "Item not found or permission denied.", variant: "destructive" });
      return null;
    }
    const originalItem = mapFirestoreDocToWishlistItem(itemSnap);
    const dataToUpdate: any = { ...itemDetails, updatedAt: serverTimestamp() };
    
    // Add uniqueId if it doesn't exist
    if (!originalItem.uniqueId) {
      dataToUpdate.uniqueId = generateUniqueId();
    }
    
    Object.keys(dataToUpdate).forEach(key => { if (dataToUpdate[key] === undefined) delete dataToUpdate[key]; });

    await updateDoc(itemDocRef, dataToUpdate);
    return { ...originalItem, ...itemDetails, updatedAt: new Date().toISOString() };
  } catch (error: any) {
    console.error("Error updating item in Firestore:", error);
    toast({ title: "Update Failed", description: `Could not update item. ${getErrorMessage(error)}`, variant: "destructive" });
    return null;
  }
};

export const removeItemFromWishlist = async (
  itemId: string,
  currentUser: AppUser
): Promise<string | null> => {
  if (!db) return null;
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) {
      toast({ title: "Error", description: "Item not found or permission denied.", variant: "destructive" });
      return null;
    }
    await deleteDoc(itemDocRef);
    return itemId;
  } catch (error: any) {
    console.error("Error deleting item from Firestore:", error);
    toast({ title: "Delete Failed", description: `Could not delete item. ${getErrorMessage(error)}`, variant: "destructive" });
    return null;
  }
};

export const updateItemCommitments = async (
  itemId: string,
  updateFn: (commitments: Commitment[]) => Commitment[] | null // updateFn is expected to return commitments with ISO string dates
): Promise<WishlistItem | null> => {
  if (!db) return null;
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists()) {
      toast({ title: "Error", description: "Item not found for commitment.", variant: "destructive" });
      return null;
    }
    const itemData = mapFirestoreDocToWishlistItem(itemSnap);
    
    // Ensure current commitments from Firestore have ISO string dates and IDs
    const currentCommitments = (Array.isArray(itemData.commitments) ? itemData.commitments : []).map(c => ({
      ...c,
      id: c.id || c.commitmentId || uuidv4(), // Ensure ID
      createdAt: c.createdAt ? formatTimestamp(c.createdAt) || new Date().toISOString() : new Date().toISOString(), // Ensure createdAt is string
    }));

    const newCommitmentsArray = updateFn(currentCommitments); // updateFn is passed from WishlistContext, should handle its own date logic for new/updated commitments

    if (newCommitmentsArray === null) return itemData; // No changes

    // Prepare commitments for Firestore: ensure createdAt is an ISO string.
    // Firestore will convert valid ISO strings to Timestamps upon saving.
    // serverTimestamp() cannot be used inside arrays.
    const commitmentsForFirestore = newCommitmentsArray.map(c => {
      const { id, committedByUid, quantity, type, createdAt } = c; // Destructure to ensure only valid fields
      return { 
        id, 
        committedByUid, 
        quantity, 
        type, 
        createdAt: createdAt || new Date().toISOString() // Ensure createdAt is always a string
      };
    });

    const updateData: any = {
      commitments: commitmentsForFirestore, // commitments is an array of objects
      updatedAt: serverTimestamp() // This is a top-level field, serverTimestamp() is fine here
    };
    
    // Add uniqueId if it doesn't exist
    if (!itemData.uniqueId) {
      updateData.uniqueId = generateUniqueId();
    }
    
    await updateDoc(itemDocRef, updateData);
    
    // Return the item with updated commitments, ensuring client-side dates are ISO strings
    return { 
        ...itemData, 
        commitments: newCommitmentsArray.map(c => ({...c, createdAt: c.createdAt || new Date().toISOString() })), 
        updatedAt: new Date().toISOString() 
    };
  } catch (error) {
    console.error("Error in updateItemCommitments:", error);
    toast({ title: "Commitment Error", description: `Could not update item commitment. ${getErrorMessage(error)}`, variant: "destructive" });
    return null;
  }
};

export const markItemAsReceived = async (itemId: string, quantityReceived: number, currentUser: AppUser): Promise<WishlistItem | null> => {
  if (!db) return null;
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) {
      toast({ title: "Error", description: "Item not found or permission denied.", variant: "destructive" });
      return null;
    }
    const itemData = mapFirestoreDocToWishlistItem(itemSnap);
    const currentReceived = itemData.receivedQuantity || 0;
    const newReceived = Math.min(itemData.quantity, currentReceived + quantityReceived);
    const updateData: any = { receivedQuantity: newReceived, updatedAt: serverTimestamp() };
    
    // Add uniqueId if it doesn't exist
    if (!itemData.uniqueId) {
      updateData.uniqueId = generateUniqueId();
    }
    
    await updateDoc(itemDocRef, updateData);
    return { ...itemData, receivedQuantity: newReceived, updatedAt: new Date().toISOString() };
  } catch (error) {
    console.error("Error marking item as received:", error);
    toast({ title: "Update Failed", description: "Could not mark item as received.", variant: "destructive" });
    return null;
  }
};

export const unmarkItemAsReceived = async (itemId: string, quantityToUnreceive: number, currentUser: AppUser): Promise<WishlistItem | null> => {
  if (!db) return null;
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists() || itemSnap.data()?.ownerUid !== currentUser.id) {
      toast({ title: "Error", description: "Item not found or permission denied.", variant: "destructive" });
      return null;
    }
    const itemData = mapFirestoreDocToWishlistItem(itemSnap);
    const newReceived = Math.max(0, (itemData.receivedQuantity || 0) - quantityToUnreceive);
    const updateData: any = { receivedQuantity: newReceived, updatedAt: serverTimestamp() };
    
    // Add uniqueId if it doesn't exist
    if (!itemData.uniqueId) {
      updateData.uniqueId = generateUniqueId();
    }
    
    await updateDoc(itemDocRef, updateData);
    return { ...itemData, receivedQuantity: newReceived, updatedAt: new Date().toISOString() };
  } catch (error) {
    console.error("Error unmarking item as received:", error);
    toast({ title: "Update Failed", description: "Could not unmark item as received.", variant: "destructive" });
    return null;
  }
};

export const addCategoryToFirestore = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>, currentAdminUser: AppUser | null): Promise<Category | null> => {
  if (!currentAdminUser?.isAdmin || !db) return null;
  try {
    const docRef = await addDoc(collection(db, "categories"), { ...categoryData, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
    return { ...categoryData, id: docRef.id, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
  } catch (error) { console.error("Error adding category:", error); toast({ title: "Category Error", description: "Could not add category.", variant: "destructive" }); return null; }
};

export const updateCategoryInFirestore = async (categoryId: string, categoryData: Partial<Omit<Category, 'id' | 'createdAt' | 'updatedAt'>>, currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  try {
    await updateDoc(doc(db, "categories", categoryId), { ...categoryData, updatedAt: serverTimestamp() });
    return true;
  } catch (error) { console.error("Error updating category:", error); toast({ title: "Category Error", description: "Could not update category.", variant: "destructive" }); return false; }
};

export const deleteCategoryFromFirestore = async (categoryId: string, allCurrentItems: WishlistItem[], currentAdminUser: AppUser | null): Promise<{ success: boolean; message?: string }> => {
  if (!currentAdminUser?.isAdmin || !db) return { success: false, message: "Permission denied." };
  const itemsUsingThisCategory = allCurrentItems.filter(item => item.categoryId === categoryId);
  if (itemsUsingThisCategory.length > 0) return { success: false, message: `Cannot delete. ${itemsUsingThisCategory.length} item(s) use this category.` };
  try {
    await deleteDoc(doc(db, "categories", categoryId));
    return { success: true };
  } catch (error) { console.error("Error deleting category:", error); return { success: false, message: `Could not delete category. ${getErrorMessage(error)}` }; }
};

export const addGroupToFirestore = async (groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>, currentAdminUser: AppUser | null): Promise<Group | null> => {
  if (!currentAdminUser?.isAdmin || !db) return null;
  try {
    const docRef = await addDoc(collection(db, "groups"), { ...groupData, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
    return { ...groupData, id: docRef.id, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
  } catch (error) { console.error("Error adding group:", error); toast({ title: "Group Error", description: "Could not add group.", variant: "destructive" }); return null; }
};

export const updateGroupInFirestore = async (groupId: string, groupData: Partial<Omit<Group, 'id' | 'createdAt' | 'updatedAt'>>, currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  try {
    await updateDoc(doc(db, "groups", groupId), { ...groupData, updatedAt: serverTimestamp() });
    return true;
  } catch (error) { console.error("Error updating group:", error); toast({ title: "Group Error", description: "Could not update group.", variant: "destructive" }); return false; }
};

export const deleteGroupFromFirestore = async (groupId: string, allCurrentUsers: AppUser[], allCurrentEvents: Event[], currentAdminUser: AppUser | null): Promise<{ success: boolean; message?: string }> => {
  if (!currentAdminUser?.isAdmin || !db) return { success: false, message: "Permission denied." };

  const usersInGroupClient = allCurrentUsers.filter(user => (user.groups || []).includes(groupId));
  if (usersInGroupClient.length > 0) return { success: false, message: `Cannot delete. ${usersInGroupClient.length} user(s) in this group based on current data.` };
  const eventsInGroupClient = allCurrentEvents.filter(event => (event.associatedGroupIds || []).includes(groupId));
  if (eventsInGroupClient.length > 0) return { success: false, message: `Cannot delete. ${eventsInGroupClient.length} event(s) use this group based on current data.` };

  try {
    const usersInGroupQuery = query(collection(db, "users"), where("groups", "array-contains", groupId), limit(1));
    const usersInGroupSnap = await getDocs(usersInGroupQuery);
    if (!usersInGroupSnap.empty) return { success: false, message: `Cannot delete. ${usersInGroupSnap.size} user(s) in this group (server check).` };

    const eventsInGroupQuery = query(collection(db, "events"), where("associatedGroupIds", "array-contains-any", [groupId]), limit(1));
    const eventsSnap = await getDocs(eventsInGroupQuery);
    if (!eventsSnap.empty) return { success: false, message: `Cannot delete. ${eventsSnap.size} event(s) use this group (server check).` };

    await deleteDoc(doc(db, "groups", groupId));
    return { success: true };
  } catch (error) { console.error("Error deleting group:", error); return { success: false, message: `Could not delete group from Firestore. ${getErrorMessage(error)}` }; }
};

export const addRankToFirestore = async (rankData: Omit<Rank, 'id' | 'createdAt' | 'updatedAt'>, currentAdminUser: AppUser | null): Promise<Rank | null> => {
  if (!currentAdminUser?.isAdmin || !db) return null;
  try {
    const docRef = await addDoc(collection(db, "ranks"), { ...rankData, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
    return { ...rankData, id: docRef.id, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
  } catch (error) { console.error("Error adding rank:", error); toast({ title: "Rank Error", description: "Could not add rank.", variant: "destructive" }); return null; }
};

export const updateRankInFirestore = async (rankId: string, rankData: Partial<Omit<Rank, 'id' | 'createdAt' | 'updatedAt'>>, currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  try {
    await updateDoc(doc(db, "ranks", rankId), { ...rankData, updatedAt: serverTimestamp() });
    return true;
  } catch (error) { console.error("Error updating rank:", error); toast({ title: "Rank Error", description: "Could not update rank.", variant: "destructive" }); return false; }
};

export const deleteRankFromFirestore = async (rankId: string, allCurrentItems: WishlistItem[], currentAdminUser: AppUser | null): Promise<{ success: boolean; message?: string }> => {
  if (!currentAdminUser?.isAdmin || !db) return { success: false, message: "Permission denied." };
  const itemsUsingThisRank = allCurrentItems.filter(item => item.rankId === rankId);
  if (itemsUsingThisRank.length > 0) return { success: false, message: `Cannot delete. ${itemsUsingThisRank.length} item(s) use this rank.` };
  try {
    await deleteDoc(doc(db, "ranks", rankId));
    return { success: true };
  } catch (error) { console.error("Error deleting rank:", error); return { success: false, message: `Could not delete rank. ${getErrorMessage(error)}` }; }
};

export const updateUserInFirestore = async (userId: string, userData: Partial<Omit<AppUser, 'id' | 'avatarUrl' | 'password' | 'createdAt' | 'updatedAt'>>, currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  const userToUpdateDocRef = doc(db, "users", userId);
  try {
    const userToUpdateSnap = await getDoc(userToUpdateDocRef);
    if (!userToUpdateSnap.exists()) {
      toast({ title: "Update Failed", description: "User not found.", variant: "destructive" });
      return false;
    }
    const existingUserData = userToUpdateSnap.data() as AppUser;
    if (userData.email && userData.email !== existingUserData.email) {
      const q = query(collection(db, "users"), where("email", "==", userData.email), limit(1));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty && querySnapshot.docs[0].id !== userId) {
        toast({ title: "Update Failed", description: `Email "${userData.email}" is already in use.`, variant: "destructive" });
        return false;
      }
    }
    const dataToUpdate: any = { ...userData, updatedAt: serverTimestamp() };
    if (userData.email === undefined) dataToUpdate.email = existingUserData.email; // Preserve email if not explicitly changed
    Object.keys(dataToUpdate).forEach(key => { if (dataToUpdate[key] === undefined) delete dataToUpdate[key]; });

    await updateDoc(userToUpdateDocRef, dataToUpdate);
    return true;
  } catch (error) { console.error("Error updating user:", error); toast({ title: "Update Failed", description: `Could not update user. ${getErrorMessage(error)}`, variant: "destructive" }); return false; }
};

export const deleteUserAndDataFromFirestore = async (userIdToDelete: string, currentAdminUser: AppUser, allUsers: AppUser[]): Promise<{ success: boolean; message?: string, deletedUserName?: string }> => {
  if (!currentAdminUser?.isAdmin || !db) { return { success: false, message: "Permission denied." }; }
  if (currentAdminUser.id === userIdToDelete) return { success: false, message: "Admins cannot delete their own account." };

  const userToDeleteProfile = allUsers.find(u => u.id === userIdToDelete);
  if (!userToDeleteProfile) return { success: false, message: "User profile not found in current data." };

  if (userToDeleteProfile.isAdmin) {
    const otherAdmins = allUsers.filter(u => u.isAdmin && u.id !== userIdToDelete);
    if (otherAdmins.length === 0) return { success: false, message: "Cannot delete the last admin user." };
  }
  try {
    const batch = writeBatch(db);
    batch.delete(doc(db, "users", userIdToDelete));
    batch.delete(doc(db, "wishlists", userIdToDelete));

    const itemsToDeleteSnap = await getDocs(query(collection(db, "items"), where("ownerUid", "==", userIdToDelete)));
    itemsToDeleteSnap.forEach(itemDoc => {
      batch.delete(itemDoc.ref);
    });

    const allOtherItemsSnap = await getDocs(query(collection(db, "items"), where("ownerUid", "!=", userIdToDelete)));
    allOtherItemsSnap.forEach(itemDoc => {
      const itemData = itemDoc.data() as WishlistItem;
      if (Array.isArray(itemData.commitments)) {
        const originalCommitmentsLength = itemData.commitments.length;
        const filteredCommitments = itemData.commitments.filter(c => c.committedByUid !== userIdToDelete);
        if (filteredCommitments.length !== originalCommitmentsLength) {
          batch.update(itemDoc.ref, { commitments: filteredCommitments, updatedAt: serverTimestamp() });
        }
      }
    });

    const messagesToUserSnap = await getDocs(query(collection(db, "messages"), where("recipientUid", "==", userIdToDelete)));
    messagesToUserSnap.forEach(msgDoc => batch.delete(msgDoc.ref));
    const messagesFromUserSnap = await getDocs(query(collection(db, "messages"), where("actingUid", "==", userIdToDelete)));
    messagesFromUserSnap.forEach(msgDoc => batch.delete(msgDoc.ref));
    const eventsByUserSnap = await getDocs(query(collection(db, "events"), where("createdByUserId", "==", userIdToDelete)));
    eventsByUserSnap.forEach(eventDoc => batch.delete(eventDoc.ref));

    await batch.commit();
    toast({ title: "User Data Deleted", description: `Firestore data for ${userToDeleteProfile.name} deleted. Firebase Auth user must be deleted separately.` });
    return { success: true, deletedUserName: userToDeleteProfile.name };
  } catch (error: any) {
    console.error("Error deleting user data:", error);
    toast({ title: "Delete User Failed", description: `Could not delete user data. ${getErrorMessage(error)}`, variant: "destructive" });
    return { success: false, message: "Could not delete user data from Firestore." };
  }
};

export const updateAnyWishlistItemInFirestore = async (
  itemId: string,
  itemDetails: Partial<Omit<WishlistItem, 'id' | 'ownerUid' | 'wishlistId' | 'commitments' | 'receivedQuantity' | 'dateAdded' | 'createdAt' | 'updatedAt'>>,
  currentAdminUser: AppUser | null
): Promise<WishlistItem | null> => {
  if (!currentAdminUser?.isAdmin || !db) {
    toast({ title: "Permission Denied", variant: "destructive" });
    return null;
  }
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists()) {
      toast({ title: "Error", description: "Item not found.", variant: "destructive" });
      return null;
    }
    const originalItem = mapFirestoreDocToWishlistItem(itemSnap);
    const dataToUpdate: any = { ...itemDetails, updatedAt: serverTimestamp() };
    Object.keys(dataToUpdate).forEach(key => { if (dataToUpdate[key] === undefined) delete dataToUpdate[key]; });

    await updateDoc(itemDocRef, dataToUpdate);
    return { ...originalItem, ...itemDetails, updatedAt: new Date().toISOString() };
  } catch (error) {
    console.error("Error updating item (admin):", error);
    toast({ title: "Update Failed", description: `Could not update item. ${getErrorMessage(error)}`, variant: "destructive" });
    return null;
  }
};

export const deleteAnyWishlistItemByAdminFromFirestore = async (
  itemId: string,
  currentAdminUser: AppUser | null
): Promise<string | null> => {
  if (!currentAdminUser?.isAdmin || !db) {
    toast({ title: "Permission Denied", variant: "destructive" });
    return null;
  }
  const itemDocRef = doc(db, "items", itemId);
  try {
    const itemSnap = await getDoc(itemDocRef);
    if (!itemSnap.exists()) {
      toast({ title: "Error", description: "Item to delete not found.", variant: "destructive" });
      return null;
    }
    await deleteDoc(itemDocRef);
    return itemId;
  } catch (error) {
    console.error("Error deleting item (admin):", error);
    toast({ title: "Delete Failed", description: `Could not delete item. ${getErrorMessage(error)}`, variant: "destructive" });
    return null;
  }
};

export const deleteAllItemsFromFirestore = async (currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) {
    toast({ title: "Permission Denied", description: "Admin only.", variant: "destructive" });
    return false;
  }
  try {
    const batch = writeBatch(db);
    const itemsSnapshot = await getDocs(collection(db, "items"));
    itemsSnapshot.forEach(itemDoc => {
      batch.delete(itemDoc.ref);
    });

    // Also update the 'updatedAt' timestamp for all wishlist shells
    const wishlistShellsSnapshot = await getDocs(collection(db, "wishlists"));
    wishlistShellsSnapshot.forEach(shellDoc => {
      batch.update(shellDoc.ref, { updatedAt: serverTimestamp() });
    });
    await batch.commit();
    return true;
  } catch (error) {
    console.error("Error deleting all items:", error);
    toast({ title: "Operation Failed", description: "Could not delete all items.", variant: "destructive" });
    return false;
  }
};
