
import { db } from '@/lib/firebase';
import { toast } from '@/hooks/use-toast';
import { collection, addDoc, serverTimestamp, writeBatch, doc, getDocs, query, where, orderBy, limit, deleteDoc, type FieldValue } from 'firebase/firestore';
import type { User as AppUser, Message, WishlistItem } from '@/lib/types';
import { formatTimestamp } from '@/lib/wishlistUtils';
import { format } from 'date-fns';

export const addMessageToList = async (messageData: Omit<Message, 'id' | 'timestamp' | 'isRead' | 'updatedAt'>): Promise<Message | null> => {
  if (!db) {
    console.error("Firestore DB not initialized. Cannot add message.");
    return null;
  }
  const dataForFirestore: Partial<Omit<Message, 'id'>> & { timestamp: FieldValue; updatedAt: FieldValue; isRead: boolean } = {
    recipientUid: messageData.recipientUid,
    eventType: messageData.eventType,
    content: messageData.content,
    isRead: false,
    timestamp: serverTimestamp(),
    updatedAt: serverTimestamp(),
  };
  if (messageData.actingUid !== undefined) dataForFirestore.actingUid = messageData.actingUid;
  if (messageData.actingUserName !== undefined) dataForFirestore.actingUserName = messageData.actingUserName;
  if (messageData.itemInfo !== undefined) dataForFirestore.itemInfo = messageData.itemInfo;
  if (messageData.commitmentInfo !== undefined) dataForFirestore.commitmentInfo = messageData.commitmentInfo;
  if (messageData.adminReason !== undefined) dataForFirestore.adminReason = messageData.adminReason;
  if (messageData.changes !== undefined) dataForFirestore.changes = messageData.changes;

  try {
    const docRef = await addDoc(collection(db, "messages"), dataForFirestore);
    const newMessageForClientState: Message = {
      id: docRef.id,
      ...messageData,
      timestamp: new Date().toISOString(), // Placeholder, will be server timestamp
      updatedAt: new Date().toISOString(), // Placeholder
      isRead: false,
    };
    return newMessageForClientState;
  } catch (error) {
    console.error("Error adding message to Firestore: ", error);
    toast({ title: "Notification Error", description: "Could not create an in-app notification.", variant: "destructive" })
    return null;
  }
};

export const generateAndDispatchNotifications = async (
  eventType: Message['eventType'] | 'user_deleted_commitment_impacted',
  actingUser: AppUser | { id: 'system', name: 'System Admin' },
  itemInfo: Message['itemInfo'],
  itemOwnerUserId: string,
  allUsers: AppUser[], // Pass allUsers for lookups
  originalItemForCommitmentsCheck?: WishlistItem,
  deletedUserName?: string,
  adminReason?: string,
): Promise<Message[]> => {
  const createdMessages: Message[] = [];
  const itemOwnerProfile = allUsers.find(u => u.id === itemOwnerUserId);
  const actingUserNameDisplay = actingUser.name || (actingUser.id === 'system' ? 'System Admin' : 'A user');
  const itemNameDisplay = itemInfo?.name || originalItemForCommitmentsCheck?.name || 'Unknown Item';
  const itemOwnerNameDisplay = itemOwnerProfile?.name || deletedUserName || 'Unknown User';

  if (['item_added', 'item_updated', 'item_deleted_by_owner', 'item_updated_by_admin'].includes(eventType) && itemOwnerProfile) {
    const relevantUsers = allUsers.filter(u =>
      u.id !== actingUser.id && u.id !== itemOwnerUserId && u.isApproved &&
      (itemOwnerProfile.groups || []).some(gId => (u.groups || []).includes(gId))
    );
    let baseMessageContent = "";
    switch (eventType) {
      case 'item_added': baseMessageContent = `${actingUserNameDisplay} added "${itemNameDisplay}" to ${itemOwnerNameDisplay}'s wishlist.`; break;
      case 'item_updated': baseMessageContent = `${actingUserNameDisplay} updated "${itemInfo?.oldName || itemNameDisplay}" (now "${itemNameDisplay}") on ${itemOwnerNameDisplay}'s wishlist.`; break;
      case 'item_deleted_by_owner': baseMessageContent = `${actingUserNameDisplay} deleted "${itemNameDisplay}" from their wishlist.`; break;
      case 'item_updated_by_admin': baseMessageContent = `An administrator (${actingUserNameDisplay}) updated ${itemOwnerNameDisplay}'s wishlist item: "${itemNameDisplay}".`; break;
    }
    for (const recipient of relevantUsers) {
      const msg = await addMessageToList({
        recipientUid: recipient.id, actingUid: actingUser.id, actingUserName: actingUser.name,
        eventType: eventType as Message['eventType'],
        itemInfo: { name: itemNameDisplay, ownerName: itemOwnerNameDisplay, id: itemInfo?.id || originalItemForCommitmentsCheck?.id },
        content: baseMessageContent,
        changes: eventType === 'item_updated_by_admin' ? itemInfo?.changes : undefined,
      });
      if (msg) createdMessages.push(msg);
    }
  }

  if (eventType === 'item_deleted_by_admin' && itemOwnerProfile && actingUser.id !== itemOwnerUserId) {
    const msg = await addMessageToList({
      recipientUid: itemOwnerUserId, actingUid: actingUser.id, actingUserName: actingUser.name,
      eventType: 'item_deleted_by_admin',
      itemInfo: { name: itemNameDisplay, ownerName: itemOwnerNameDisplay, id: itemInfo?.id || originalItemForCommitmentsCheck?.id },
      content: `An administrator (${actingUserNameDisplay}) deleted your item "${itemNameDisplay}". Reason: ${adminReason || 'No reason provided.'}`,
      adminReason: adminReason
    });
    if (msg) createdMessages.push(msg);
  }
  if (eventType === 'item_updated_by_admin' && itemOwnerProfile && actingUser.id !== itemOwnerUserId) {
    const msg = await addMessageToList({
      recipientUid: itemOwnerUserId, actingUid: actingUser.id, actingUserName: actingUser.name,
      eventType: 'item_updated_by_admin',
      itemInfo: { name: itemNameDisplay, ownerName: itemOwnerNameDisplay, id: itemInfo?.id || originalItemForCommitmentsCheck?.id, changes: itemInfo?.changes },
      content: `An administrator (${actingUserNameDisplay}) updated your item "${itemNameDisplay}". Please review the changes.`,
      changes: itemInfo?.changes
    });
    if (msg) createdMessages.push(msg);
  }

  const commitmentsToNotify = originalItemForCommitmentsCheck?.commitments || [];
  if (eventType === 'item_deleted_by_owner' || eventType === 'item_deleted_by_admin' || eventType === 'user_deleted_commitment_impacted') {
    for (const commitment of commitmentsToNotify) {
      const committerProfile = allUsers.find(u => u.id === commitment.committedByUid);
      if (committerProfile && !(eventType === 'user_deleted_commitment_impacted' && committerProfile.id === actingUser.id)) {
        let commitmentImpactMessage = "";
        const commitmentTypeDisplay = commitment.type === 'reserved' ? 'reservation' : 'purchase';
        const originalItemName = originalItemForCommitmentsCheck?.name || 'Unknown Item';
        const effectiveOwnerName = eventType === 'user_deleted_commitment_impacted' ? (deletedUserName || 'A user') : (itemOwnerProfile?.name || itemOwnerNameDisplay);

        if (eventType === 'item_deleted_by_owner') {
          commitmentImpactMessage = `The item "${originalItemName}" (for ${effectiveOwnerName}), for which you had a ${commitmentTypeDisplay} of ${commitment.quantity}, has been deleted by the owner.`;
        } else if (eventType === 'item_deleted_by_admin') {
          commitmentImpactMessage = `The item "${originalItemName}" (for ${effectiveOwnerName}), for which you had a ${commitmentTypeDisplay} of ${commitment.quantity}, has been deleted by an administrator. Reason: ${adminReason || 'No reason provided.'}`;
        } else if (eventType === 'user_deleted_commitment_impacted') {
          commitmentImpactMessage = `User '${effectiveOwnerName}' and their wishlist have been removed. Your ${commitmentTypeDisplay} for their item '${originalItemName}' (${commitment.quantity}) is no longer applicable.`;
        }

        if (commitmentImpactMessage) {
          const msg = await addMessageToList({
            recipientUid: committerProfile.id, actingUid: actingUser.id, actingUserName: actingUser.name,
            eventType: 'item_commitment_impacted_by_deletion',
            itemInfo: { name: originalItemName, ownerName: effectiveOwnerName, id: originalItemForCommitmentsCheck?.id },
            commitmentInfo: { type: commitment.type, quantity: commitment.quantity },
            content: commitmentImpactMessage,
            adminReason: eventType === 'item_deleted_by_admin' ? adminReason : undefined,
          });
          if (msg) createdMessages.push(msg);
        }
      }
    }
  }
  return createdMessages;
};

export const markMessagesAsRead = async (userId: string, currentMessages: Message[]): Promise<string[] | null> => {
  if (!db) return null;
  const idsToMark = currentMessages.filter(msg => msg.recipientUid === userId && !msg.isRead).map(msg => msg.id);
  if (idsToMark.length === 0) return [];

  const batch = writeBatch(db);
  idsToMark.forEach(id => {
    batch.update(doc(db, "messages", id), { isRead: true, updatedAt: serverTimestamp() });
  });
  try {
    await batch.commit();
    return idsToMark;
  } catch (error) {
    console.error("Error marking messages as read in Firestore:", error);
    return null;
  }
};

export const deleteNotificationById = async (messageId: string, currentAdminUser: AppUser | null): Promise<boolean> => {
  if (!currentAdminUser?.isAdmin || !db) return false;
  try {
    await deleteDoc(doc(db, "messages", messageId));
    return true;
  } catch (error) {
    console.error("Error deleting notification by ID:", error);
    toast({ title: "Delete Failed", description: "Could not delete the notification.", variant: "destructive" });
    return false;
  }
};

export const deleteAllNotificationsForUser = async (userId: string): Promise<boolean> => {
  if (!db) {
    console.error("Firestore DB not initialized. Cannot delete notifications.");
    return false;
  }
  
  try {
    const batch = writeBatch(db);
    const userMessagesQuery = query(
      collection(db, "messages"),
      where("recipientUid", "==", userId)
    );
    const userMessagesSnap = await getDocs(userMessagesQuery);
    
    userMessagesSnap.docs.forEach(docSnap => {
      batch.delete(docSnap.ref);
    });
    
    await batch.commit();
    return true;
  } catch (error) {
    console.error("Error deleting user notifications:", error);
    toast({ 
      title: "Delete Failed", 
      description: "Could not delete your notifications. Please try again.", 
      variant: "destructive" 
    });
    return false;
  }
};

export const deleteAllNotificationsAndNotifyUsers = async (currentAdminUser: AppUser | null, allUsers: AppUser[]): Promise<Message[]> => {
  const newMessages: Message[] = [];
  if (!currentAdminUser?.isAdmin || !db) return newMessages;
  try {
    const batch = writeBatch(db);
    const messagesSnap = await getDocs(collection(db, "messages"));
    messagesSnap.docs.forEach(docSnap => batch.delete(docSnap.ref));

    const resetContent = `Notification history was reset by an admin on ${format(new Date(), "PPP p")}.`;
    for (const userDocSnap of allUsers) { // Assuming allUsers are fetched elsewhere
        const messageDataForReset: Omit<Message, 'id'|'timestamp'|'updatedAt'|'isRead'> & { timestamp: FieldValue; updatedAt: FieldValue; isRead: boolean } = {
            recipientUid: userDocSnap.id,
            actingUid: "system",
            actingUserName: "System Admin",
            eventType: "system_notification_reset",
            content: resetContent,
            isRead: false,
            timestamp: serverTimestamp(),
            updatedAt: serverTimestamp()
        };
        const newMsgRef = doc(collection(db, "messages")); // Create ref for new message
        batch.set(newMsgRef, messageDataForReset);
        // Add placeholder for client state update
        newMessages.push({
            id: newMsgRef.id, // This ID is client-side only until Firestore confirms
            ...messageDataForReset,
            timestamp: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isRead: false
        } as Message);
    }
    await batch.commit();
    return newMessages; // Return messages created, to be added to local state by context
  } catch (error) {
    console.error("Error deleting all notifications and notifying users:", error);
    toast({ title: "Action Failed", description: "Could not delete all notifications.", variant: "destructive" });
    return []; // Return empty array on failure
  }
};
